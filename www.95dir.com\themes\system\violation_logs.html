<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>违规检查日志</title>
<link href="../themes/system/skin/global.css" rel="stylesheet" type="text/css" />
<link href="../themes/system/skin/iframe.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../public/scripts/jquery.min.js"></script>
</head>

<body>
<div id="wrapper">
    <div id="header">
        <h1>违规检查日志</h1>
        <div class="nav-tabs">
            <a href="violation_simple_working.php?action=list">违规列表</a>
            <a href="violation_simple_working.php?action=config">配置管理</a>
            <a href="violation_simple_working.php?action=logs" class="active">检查日志</a>
            <a href="violation_simple_working.php?action=check">手动检查</a>
        </div>
    </div>
    
    <div id="content">
        <div class="stats-info">
            <p>共有 <strong>{#$total#}</strong> 条违规检查记录</p>
        </div>
        
        <!-- 违规日志列表 -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th width="60">ID</th>
                        <th width="60">网站ID</th>
                        <th width="200">网站名称</th>
                        <th width="200">网站URL</th>
                        <th width="300">违规原因</th>
                        <th width="120">检查时间</th>
                        <th width="100">检查IP</th>
                    </tr>
                </thead>
                <tbody>
                    {#foreach from=$logs item=log#}
                    <tr>
                        <td>{#$log.log_id#}</td>
                        <td>{#$log.web_id#}</td>
                        <td>
                            {#if $log.web_name#}
                                {#$log.web_name#}
                            {#else#}
                                <span style="color: #999;">已删除</span>
                            {#/if#}
                        </td>
                        <td>
                            {#if $log.web_url#}
                                <span style="color: #666; font-size: 12px;">
                                    {#$log.web_url|truncate:30#}
                                </span>
                            {#else#}
                                <span style="color: #999;">-</span>
                            {#/if#}
                        </td>
                        <td>
                            <span style="color: #d9534f;">
                                {#$log.violation_reason#}
                            </span>
                        </td>
                        <td>
                            {#$log.check_time|date_format:"%m-%d %H:%M:%S"#}
                        </td>
                        <td>
                            <span style="font-size: 12px; color: #666;">
                                {#$log.check_ip#}
                            </span>
                        </td>
                    </tr>
                    {#foreachelse#}
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 50px; color: #999;">
                            暂无违规检查记录
                        </td>
                    </tr>
                    {#/foreach#}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {#if $pages > 1#}
        <div class="pagination">
            {#if $page > 1#}
            <a href="violation_simple_working.php?action=logs&page={#$page-1#}" class="btn">上一页</a>
            {#/if#}
            
            {#for $i=1 to $pages#}
                {#if $i == $page#}
                <span class="current">{#$i#}</span>
                {#else#}
                <a href="violation_simple_working.php?action=logs&page={#$i#}">{#$i#}</a>
                {#/if#}
            {#/for#}
            
            {#if $page < $pages#}
            <a href="violation_simple_working.php?action=logs&page={#$page+1#}" class="btn">下一页</a>
            {#/if#}
        </div>
        {#/if#}
        
        <div class="info-box">
            <h3>日志说明：</h3>
            <ul>
                <li>此日志记录了所有被检测为违规的网站信息</li>
                <li>包括违规的具体原因和检测时间</li>
                <li>可以根据日志分析违规趋势和调整关键词配置</li>
                <li>日志会永久保存，便于后续审计和分析</li>
            </ul>
        </div>
    </div>
</div>

<style>
.nav-tabs {
    margin: 10px 0;
    border-bottom: 1px solid #ddd;
}

.nav-tabs a {
    display: inline-block;
    padding: 8px 16px;
    margin-right: 5px;
    text-decoration: none;
    color: #666;
    border: 1px solid #ddd;
    border-bottom: none;
    background: #f5f5f5;
}

.nav-tabs a.active {
    background: #fff;
    color: #333;
    font-weight: bold;
}

.stats-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
}

.table-container {
    margin: 20px 0;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: #f5f5f5;
    padding: 10px;
    border-bottom: 1px solid #ddd;
    text-align: left;
    font-weight: bold;
}

.data-table td {
    padding: 10px;
    border-bottom: 1px solid #eee;
    vertical-align: top;
}

.data-table tr:hover {
    background: #f9f9f9;
}

.pagination {
    text-align: center;
    margin: 20px 0;
}

.pagination a, .pagination span {
    display: inline-block;
    padding: 5px 10px;
    margin: 0 2px;
    text-decoration: none;
    border: 1px solid #ddd;
}

.pagination .current {
    background: #337ab7;
    color: white;
    border-color: #337ab7;
}

.btn {
    display: inline-block;
    padding: 5px 10px;
    text-decoration: none;
    border-radius: 3px;
    font-size: 12px;
    background: #f5f5f5;
    border: 1px solid #ddd;
}

.info-box {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
}

.info-box h3 {
    margin-top: 0;
    color: #333;
}

.info-box ul {
    margin: 10px 0;
    padding-left: 20px;
}

.info-box li {
    margin: 5px 0;
    color: #666;
}
</style>

</body>
</html>
