<?php
/**
 * Smarty 4.x 配置文件
 * 兼容原有项目的Smarty配置
 */

require(APP_PATH.'extend/smarty/Smarty.class.php');

$template_dir = ROOT_PATH.THEME_DIR.'/';
$compile_dir = ROOT_PATH.'data/compile/';
$cache_dir = ROOT_PATH.'data/cache/';
		
if (defined('IN_ADMIN') && IN_ADMIN == TRUE) {
	$dirname = 'system';
	$lifetime = 0;
	$caching = false;
} elseif (defined('IN_MEMBER')) {
	$dirname = 'member';
	$lifetime = 0;
	$caching = false;
} else {
	$dirname = 'default';
	$lifetime = CACHE_LIFETIME;
	$caching = CACHE_ON;
}

$smarty = new Smarty();

// 基础配置
$smarty->debugging = false;
$smarty->template_dir = $template_dir.$dirname.'/';
$smarty->compile_dir = $compile_dir.$dirname.'/';
$smarty->cache_dir = $cache_dir.$dirname.'/';

// 缓存配置
$smarty->caching = $caching;
$smarty->cache_lifetime = $lifetime;

// 自定义分隔符（保持与原项目一致）
$smarty->left_delimiter = "{#";
$smarty->right_delimiter = "#}";

// Smarty 4.x 新增配置
$smarty->compile_check = true;  // 检查模板是否需要重新编译
$smarty->force_compile = false; // 不强制编译（生产环境建议false）

// 错误报告配置
$smarty->error_reporting = E_ALL & ~E_NOTICE;

// 安全配置
$smarty->use_sub_dirs = true;   // 使用子目录存储编译文件
$smarty->allow_php_templates = false; // 不允许PHP模板（安全考虑）

// 性能优化配置
$smarty->merge_compiled_includes = true; // 合并编译的包含文件

// 确保目录存在且可写
$dirs_to_check = [
    $smarty->template_dir,
    $smarty->compile_dir,
    $smarty->cache_dir
];

foreach ($dirs_to_check as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    if (!is_writable($dir)) {
        chmod($dir, 0755);
    }
}

/** 
 * 检查模板资源是否存在
 * 兼容原有的template_exists函数
 */
function template_exists($template) {
	global $smarty;
	
	if (!$smarty->templateExists($template)){
		exit('The template file "'.$template.'" is not found!');
	}
}

/**
 * Smarty 4.x 兼容性函数
 * 处理一些可能的兼容性问题
 */

// 注册自定义修饰符（如果项目中有使用）
// $smarty->registerPlugin('modifier', 'custom_modifier', 'custom_modifier_function');

// 注册自定义函数（如果项目中有使用）
// $smarty->registerPlugin('function', 'custom_function', 'custom_function_handler');

// 设置全局变量
$smarty->assign('smarty_version', Smarty::SMARTY_VERSION);
$smarty->assign('php_version', phpversion());

// 如果需要向后兼容Smarty 3.x的某些功能，可以在这里添加
// 例如：处理一些废弃的语法或函数

/**
 * 错误处理函数
 */
function smarty_error_handler($errno, $errstr, $errfile, $errline) {
    // 记录Smarty相关错误
    if (strpos($errfile, 'smarty') !== false || strpos($errstr, 'Smarty') !== false) {
        error_log("Smarty Error: $errstr in $errfile on line $errline");
    }
    return false; // 让PHP继续处理错误
}

// 设置错误处理
set_error_handler('smarty_error_handler');

/**
 * 调试信息（开发环境使用）
 */
if (defined('DEBUG') && DEBUG === true) {
    $smarty->debugging = true;
    $smarty->force_compile = true;
    error_reporting(E_ALL);
}

/**
 * 自定义插件目录（如果项目有自定义插件）
 */
// $smarty->addPluginsDir(APP_PATH . 'smarty_plugins/');

/**
 * 模板继承和包含的路径处理
 */
$smarty->inheritance_merge_compiled_includes = false;

/**
 * 缓存处理优化
 */
if ($caching) {
    // 设置缓存ID生成函数
    $smarty->cache_id = md5($_SERVER['REQUEST_URI']);
    
    // 缓存组设置
    $smarty->cache_group = $dirname;
}

/**
 * 输出过滤器（如果需要）
 */
// $smarty->registerFilter('output', 'output_filter_function');

/**
 * 预处理过滤器（如果需要）
 */
// $smarty->registerFilter('pre', 'pre_filter_function');

/**
 * 后处理过滤器（如果需要）
 */
// $smarty->registerFilter('post', 'post_filter_function');

/**
 * 兼容性检查
 */
function checkSmartyCompatibility() {
    global $smarty;
    
    $issues = [];
    
    // 检查PHP版本
    if (version_compare(phpversion(), '7.1.0', '<')) {
        $issues[] = 'PHP版本过低，Smarty 4.x需要PHP 7.1+';
    }
    
    // 检查目录权限
    $dirs = [$smarty->template_dir, $smarty->compile_dir, $smarty->cache_dir];
    foreach ($dirs as $dir) {
        if (!is_writable($dir)) {
            $issues[] = "目录不可写: $dir";
        }
    }
    
    // 检查内存限制
    $memory_limit = ini_get('memory_limit');
    if ($memory_limit && $memory_limit !== '-1') {
        $memory_bytes = return_bytes($memory_limit);
        if ($memory_bytes < 64 * 1024 * 1024) { // 64MB
            $issues[] = '内存限制过低，建议至少64MB';
        }
    }
    
    return $issues;
}

function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}

// 在开发环境下进行兼容性检查
if (defined('DEBUG') && DEBUG === true) {
    $compatibility_issues = checkSmartyCompatibility();
    if (!empty($compatibility_issues)) {
        foreach ($compatibility_issues as $issue) {
            trigger_error("Smarty兼容性警告: $issue", E_USER_WARNING);
        }
    }
}

/**
 * 性能监控（可选）
 */
if (defined('SMARTY_PERFORMANCE_LOG') && SMARTY_PERFORMANCE_LOG === true) {
    $smarty->compile_check = true;
    
    // 记录编译时间
    register_shutdown_function(function() use ($smarty) {
        $memory_usage = memory_get_usage(true);
        $memory_peak = memory_get_peak_usage(true);
        
        error_log("Smarty性能: 内存使用 " . formatBytes($memory_usage) . 
                 ", 峰值 " . formatBytes($memory_peak));
    });
}

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB');
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}

/**
 * 清理函数
 */
function clearSmartyCache($cache_group = null) {
    global $smarty;
    
    if ($cache_group) {
        $smarty->clearCache(null, $cache_group);
    } else {
        $smarty->clearAllCache();
    }
    
    return true;
}

function clearSmartyCompiled() {
    global $smarty;
    
    $smarty->clearCompiledTemplate();
    return true;
}

// 注册清理函数为全局函数
if (!function_exists('clear_smarty_cache')) {
    function clear_smarty_cache($group = null) {
        return clearSmartyCache($group);
    }
}

if (!function_exists('clear_smarty_compiled')) {
    function clear_smarty_compiled() {
        return clearSmartyCompiled();
    }
}
?>
