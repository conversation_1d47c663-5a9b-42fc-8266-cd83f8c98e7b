body, h1, h2, h3, h4, h5, h6, hr, p, blockquote, dl, dt, dd, ul, ol, li, pre, form, fieldset, legend, button, input, textarea, th, td {margin: 0; padding: 0;}
body, button, input, select, textarea {font: 12px/1.5 "Segoe UI", tahoma, arial, \5b8b\4f53, sans-serif;}
h1, h2, h3, h4, h5, h6 {font-size: 100%;}
address, cite, dfn, em, var {font-style: normal;}
code, kbd, pre, samp {font-family: courier new, courier, monospace;}
small {font-size: 12px;}
ul, ol {list-style: none;}
a {text-decoration: none;}
a:hover {text-decoration: underline;}
sup {vertical-align: text-top;}
sub {vertical-align: text-bottom;}
legend {color: #000;}
fieldset, img {border: 0;}
button, input, select, textarea {font-size: 100%;}
table {border-collapse: collapse; border-spacing: 0;}
/* clear float */
.clearfix:after {clear: both; content: '.'; display: block; font-size: 0; height: 1; visibility: hidden;}
*html .clearfix {zoom: 1;}
*:first-child+html .clearfix {zoom: 1;}