{#include file="header.html"#}

<h3 class="title"><em>黑名单管理</em><span>共 {#$total#} 个黑名单网站</span></h3>

<div class="listbox">
    <!-- 分类筛选 -->
    <div class="toolbar">
        <select class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='{#$fileurl#}?act=list&category='+this.options[this.selectedIndex].value;}">
            <option value="">所有分类</option>
            {#foreach from=$categories key=cat_id item=cat_name#}
            <option value="{#$cat_id#}"{#if $current_category == $cat_id#} selected{#/if#}>{#$cat_name#}</option>
            {#/foreach#}
        </select>
        <span style="margin-left: 20px;">
            <a href="website.php?status=1" class="btn">网站管理</a>
        </span>
    </div>

    <table width="100%" border="0" cellspacing="1" cellpadding="0">
        <tr>
            <th width="60">ID</th>
            <th width="200">网站名称</th>
            <th width="250">网站地址</th>
            <th width="100">黑名单分类</th>
            <th width="300">拉黑理由</th>
            <th width="100">操作员</th>
            <th width="120">拉黑时间</th>
            <th width="100">操作</th>
        </tr>
        {#foreach from=$websites item=website#}
        <tr>
            <td>{#$website.web_id#}</td>
            <td class="ltext">
                <a href="{#$fileurl#}?act=detail&id={#$website.web_id#}" title="查看详情">
                    {#$website.web_name#}
                </a>
            </td>
            <td class="ltext">
                <a href="http://{#$website.web_url#}" target="_blank" title="{#$website.web_url#}">
                    {#if strlen($website.web_url) > 30#}
                        {#$website.web_url|substr:0:30#}...
                    {#else#}
                        {#$website.web_url#}
                    {#/if#}
                </a>
            </td>
            <td>
                <span style="color: #f00;">{#$website.category_name#}</span>
            </td>
            <td class="ltext" title="{#$website.web_blacklist_reason#}">
                {#$website.reason_short#}
            </td>
            <td>{#$website.web_blacklist_operator#}</td>
            <td>{#$website.blacklist_time_formatted#}</td>
            <td>
                <a href="{#$fileurl#}?act=detail&id={#$website.web_id#}">详情</a>
                |
                <a href="{#$fileurl#}?act=restore&id={#$website.web_id#}" 
                   onclick="return confirm('确认恢复此网站为待审核状态吗？');" 
                   style="color: #080;">恢复</a>
            </td>
        </tr>
        {#foreachelse#}
        <tr><td colspan="8">暂无黑名单网站</td></tr>
        {#/foreach#}
    </table>
    
    <div class="pagebox">{#$showpage#}</div>
</div>

<style>
.pagination {
    text-align: center;
    margin: 20px 0;
}

.pagination a, .pagination strong {
    display: inline-block;
    padding: 5px 10px;
    margin: 0 2px;
    text-decoration: none;
    border: 1px solid #ddd;
    color: #333;
}

.pagination a:hover {
    background-color: #f5f5f5;
}

.pagination strong {
    background-color: #007cba;
    color: white;
    border-color: #007cba;
}

.ltext {
    text-align: left !important;
    padding-left: 10px !important;
}

.toolbar {
    margin-bottom: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 3px;
}

.toolbar .sel {
    margin-right: 10px;
}

.btn {
    background: #007cba;
    color: white;
    padding: 5px 15px;
    text-decoration: none;
    border-radius: 3px;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background: #005a87;
}
</style>

{#include file="footer.html"#}
