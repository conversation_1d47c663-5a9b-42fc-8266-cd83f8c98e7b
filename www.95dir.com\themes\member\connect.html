<!DOCTYPE html>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="keywords" content="{#$site_keywords#}">
<meta name="description" content="{#$site_description#}">
<script type="text/javascript">var sitepath = '{#$site_root#}'; var rewrite = '{#$cfg.is_enabled_rewrite#}';</script>
<script type="text/javascript" src="{#$site_root#}public/scripts/jquery.min.js"></script>
<script type="text/javascript" src="{#$site_root#}public/scripts/common.js"></script>
<link href="{#$site_root#}themes/member/skin/reset.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}themes/member/skin/login.css" rel="stylesheet" type="text/css" />
</head>

<body>
<div id="wrapper">
    <div id="header">
    	<a href="{#$site_url#}" class="logo"></a>
        <div id="toplink"><a href="{#$site_url#}?mod=index">返回首页&raquo;</a></div>
    </div>
	<div id="regform" class="clearfix">
      	<h2>欢迎注册成为{#$site_name#}会员！</h2>
        <div style="font: bold 14px normal; padding-left: 250px; padding-top: 30px;"><input type="radio" name="radio" id="radio_1" checked onClick="$('#form1').show(); $('#form2').hide();" /><label for="radio_1">我还未注册</label>　　<input type="radio" name="radio" id="radio_2" onClick="$('#form1').hide(); $('#form2').show();" /><label for="radio_2">已经注册过</label></div>
        <div id="form1">
        	<form name="form1" method="post" action="?mod=register">
        	<ul>
           		<li><label><font color="#FF0000">*</font> 电子邮箱：</label><input type="text" name="email" size="40" maxlength="50" class="ipt" /><p>登录账户、找回密码时使用</p></li>
            	<li><label><font color="#FF0000">*</font> 帐号密码：</label><input type="password" name="pass" size="40" maxlength="50" class="ipt" /><p>6~20个字符，区分大小写</p></li>
            	<li><label><font color="#FF0000">*</font> 确认密码：</label><input type="password" name="pass1" size="40" maxlength="50" class="ipt" /><p>同上</p></li>
            	<li><label><font color="#FF0000">*</font> 昵 称：</label><input type="text" name="nick" size="40" maxlength="20" class="ipt" value="{#$nick_name#}" /><p>我们对您的称呼</p></li>
            	<li><label><font color="#FF0000">*</font> 腾讯QQ：</label><input type="text" name="qq" size="40" maxlength="20" class="ipt" /><p>站长相互联系</p></li>
            	<li><label><font color="#FF0000">*</font> 验 证 码：</label><input type="text" name="code" size="10" maxlength="6" class="ipt" onFocus="refreshimg('mycode');" /><span id="mycode"></span><p>点击输入框即可显示验证码</p></li>
            	<li><label>&nbsp;</label><input type="hidden" name="open_id" id="open_id" value="{#$open_id#}"><input type="hidden" name="action" value="register"><input type="submit" value="绑定帐号" class="btn" /></li>
        	</ul>
        </form>
        </div>
        
        <div id="form2" style="display: none;">
        	<form name="form2" id="form2" method="post" action="?mod=login">
        	<ul>
        		<li><label>电子邮箱：</label><input type="text" name="email" size="30" maxlength="50" class="ipt" /></li>
            	<li><label>登录密码：</label><input type="password" name="pass" size="30" maxlength="50" class="ipt" /></li>
            	<li><label>&nbsp;</label><input type="hidden" name="open_id" id="open_id" value="{#$open_id#}"><input type="hidden" name="action" value="login" /><input type="submit" value="绑定帐号" class="btn" /></li>
        	</ul>
        	</form>
        </div>
    </div>
    <div id="footer" class="clearfix">
    	{#$site_copyright#}
    </div>
</div>
</body>
</html>