<!doctype html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>Dynamic Load Examples</title>
		<style>
			form {
				margin: 0;
			}
			textarea {
				display: block;
			}
		</style>
		<script charset="utf-8" src="http://code.jquery.com/jquery-1.6.4.min.js"></script>
		<script>
			$(function() {
				var editor;
				$('input[name=load]').click(function() {
					$.getScript('../kindeditor-min.js', function() {
						KindEditor.basePath = '../';
						editor = KindEditor.create('textarea[name="content"]');
					});
				});
				$('input[name=remove]').click(function() {
					if (editor) {
						editor.remove();
						editor = null;
					}
				});
			});
		</script>
	</head>
	<body>
		<h3>异步加载</h3>
		<form>
			<textarea name="content" style="width:800px;height:200px;"></textarea>
			<p>
				<input type="button" name="load" value="加载JS并创建编辑器" />
				<input type="button" name="remove" value="删除编辑器" />
			</p>
		</form>
	</body>
</html>
