<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>手动违规检查</title>
<link href="../themes/system/skin/global.css" rel="stylesheet" type="text/css" />
<link href="../themes/system/skin/iframe.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../public/scripts/jquery.min.js"></script>
</head>

<body>
<div id="wrapper">
    <div id="header">
        <h1>手动违规检查</h1>
        <div class="nav-tabs">
            <a href="violation_simple_working.php?action=list">违规列表</a>
            <a href="violation_simple_working.php?action=config">配置管理</a>
            <a href="violation_simple_working.php?action=logs">检查日志</a>
            <a href="violation_simple_working.php?action=check" class="active">手动检查</a>
        </div>
    </div>
    
    <div id="content">
        <!-- 消息提示 -->
        {#if $message#}
        <div class="message {#$message_type#}">
            {#$message#}
        </div>
        {#/if#}
        
        <!-- 检查表单 -->
        <div class="check-form">
            <h2>执行违规检查</h2>
            <p>点击下面的按钮开始检查网站违规情况。系统会检查最近未检查的50个网站。</p>
            
            <form method="post" action="violation_simple_working.php?action=check" id="checkForm">
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" id="checkBtn">
                        <span id="btnText">开始检查</span>
                        <span id="btnLoading" style="display: none;">检查中...</span>
                    </button>
                </div>
            </form>
        </div>
        
        <!-- 检查结果 -->
        {#if $check_results#}
        <div class="results-container">
            <h2>检查结果</h2>
            
            <div class="result-summary">
                <div class="summary-item">
                    <span class="summary-number">{#$check_results.total_checked#}</span>
                    <span class="summary-label">总检查数</span>
                </div>
                <div class="summary-item violation">
                    <span class="summary-number">{#$check_results.violations_found#}</span>
                    <span class="summary-label">发现违规</span>
                </div>
                <div class="summary-item error">
                    <span class="summary-number">{#$check_results.errors#}</span>
                    <span class="summary-label">检查错误</span>
                </div>
            </div>
            
            {#if $check_results.violations_found > 0#}
            <div class="violation-details">
                <h3>违规详情</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>网站ID</th>
                            <th>网站名称</th>
                            <th>网站URL</th>
                            <th>违规原因</th>
                        </tr>
                    </thead>
                    <tbody>
                        {#foreach from=$check_results.details item=detail#}
                        <tr>
                            <td>{#$detail.web_id#}</td>
                            <td>{#$detail.web_name#}</td>
                            <td style="color: #666; font-size: 12px;">{#$detail.web_url#}</td>
                            <td style="color: #d9534f;">{#$detail.reason#}</td>
                        </tr>
                        {#/foreach#}
                    </tbody>
                </table>
            </div>
            {#else#}
            <div class="no-violations">
                <p style="color: #5cb85c; font-size: 16px; text-align: center; padding: 20px;">
                    ✓ 本次检查未发现违规网站
                </p>
            </div>
            {#/if#}
        </div>
        {#/if#}
        
        <div class="info-box">
            <h3>检查说明：</h3>
            <ul>
                <li>手动检查会检查最近未检查的50个网站</li>
                <li>检查内容包括：网站标题、描述、页面内容和可访问性</li>
                <li>发现违规的网站会自动隐藏，不在前台显示</li>
                <li>检查过程可能需要1-2分钟，请耐心等待</li>
                <li>建议定期执行手动检查，确保网站内容合规</li>
            </ul>
            
            <h3>自动检查：</h3>
            <p>系统支持定时自动检查，请确保已设置crontab定时任务：</p>
            <code>0 2 * * * /usr/bin/php {#$smarty.const.ROOT_PATH#}cron_violation_check.php</code>
        </div>
    </div>
</div>

<style>
.nav-tabs {
    margin: 10px 0;
    border-bottom: 1px solid #ddd;
}

.nav-tabs a {
    display: inline-block;
    padding: 8px 16px;
    margin-right: 5px;
    text-decoration: none;
    color: #666;
    border: 1px solid #ddd;
    border-bottom: none;
    background: #f5f5f5;
}

.nav-tabs a.active {
    background: #fff;
    color: #333;
    font-weight: bold;
}

.message {
    padding: 10px;
    margin: 10px 0;
    border-radius: 3px;
}

.message.success {
    background: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
}

.message.error {
    background: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
}

.check-form {
    background: #fff;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin: 20px 0;
    text-align: center;
}

.check-form h2 {
    margin-top: 0;
    color: #333;
}

.form-actions {
    margin: 20px 0;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 3px;
    font-size: 16px;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: #337ab7;
    color: white;
}

.btn-primary:hover {
    background: #286090;
}

.btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.results-container {
    background: #fff;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin: 20px 0;
}

.result-summary {
    display: flex;
    gap: 20px;
    margin: 20px 0;
    justify-content: center;
}

.summary-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
    text-align: center;
    min-width: 120px;
}

.summary-item.violation {
    background: #f8d7da;
}

.summary-item.error {
    background: #fff3cd;
}

.summary-number {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.summary-item.violation .summary-number {
    color: #d9534f;
}

.summary-item.error .summary-number {
    color: #f0ad4e;
}

.summary-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.violation-details {
    margin: 20px 0;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #ddd;
}

.data-table th {
    background: #f5f5f5;
    padding: 10px;
    border-bottom: 1px solid #ddd;
    text-align: left;
    font-weight: bold;
}

.data-table td {
    padding: 10px;
    border-bottom: 1px solid #eee;
    vertical-align: top;
}

.data-table tr:hover {
    background: #f9f9f9;
}

.no-violations {
    background: #dff0d8;
    border: 1px solid #d6e9c6;
    border-radius: 5px;
    margin: 20px 0;
}

.info-box {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
}

.info-box h3 {
    margin-top: 0;
    color: #333;
}

.info-box ul {
    margin: 10px 0;
    padding-left: 20px;
}

.info-box li {
    margin: 5px 0;
    color: #666;
}

.info-box code {
    background: #f5f5f5;
    padding: 5px 10px;
    border-radius: 3px;
    font-family: monospace;
    display: block;
    margin: 10px 0;
}
</style>

<script>
$(document).ready(function() {
    $('#checkForm').submit(function() {
        $('#checkBtn').prop('disabled', true);
        $('#btnText').hide();
        $('#btnLoading').show();
    });
});
</script>

</body>
</html>
