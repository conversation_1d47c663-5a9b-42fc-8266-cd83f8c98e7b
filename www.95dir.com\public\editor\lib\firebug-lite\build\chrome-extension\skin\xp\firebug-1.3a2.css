.fbBtnPressed {
    background: #ECEBE3;
    padding: 3px 6px 2px 7px !important;
    margin: 1px 0 0 1px;
    _margin: 1px -1px 0 1px;
    border: 1px solid #ACA899 !important;
    border-color: #ACA899 #ECEBE3 #ECEBE3 #ACA899 !important;
}

.fbToolbarButtons {
    display: none;
}

#fbStatusBarBox {
    display: none;
}

/************************************************************************************************
 Error Popup
*************************************************************************************************/
#fbErrorPopup {
    position: absolute;
    right: 0;
    bottom: 0;
    height: 19px;
    width: 75px;
    background: url(sprite.png) #f1f2ee 0 0;
    z-index: 999;
}

#fbErrorPopupContent {
    position: absolute;
    right: 0;
    top: 1px;
    height: 18px;
    width: 75px;
    _width: 74px;
    border-left: 1px solid #aca899;
}

#fbErrorIndicator {
    position: absolute;
    top: 2px;
    right: 5px;
}










.fbBtnInspectActive {
    background: #aaa;
    color: #fff !important;
}

/************************************************************************************************
 General
*************************************************************************************************/
html, body {
    margin: 0;
    padding: 0;
    overflow: hidden;
}

body {
    font-family: Lucida Grande, Tahoma, sans-serif;
    font-size: 11px;
    background: #fff;    
}

.clear {
    clear: both;
}

/************************************************************************************************
 Mini Chrome
*************************************************************************************************/
#fbMiniChrome {
    display: none;
    right: 0;
    height: 27px;
    background: url(sprite.png) #f1f2ee 0 0;
    margin-left: 1px;
}

#fbMiniContent {
    display: block;
    position: relative;
    left: -1px;
    right: 0;
    top: 1px;
    height: 25px;
    border-left: 1px solid #aca899;
}

#fbToolbarSearch {
    float: right;
    border: 1px solid #ccc;
    margin: 0 5px 0 0;
    background: #fff url(search.png) no-repeat 4px 2px;
    padding-left: 20px;    
    font-size: 11px;
}

#fbToolbarErrors {
    float: right;
    margin: 1px 4px 0 0;
    font-size: 11px;
}

#fbLeftToolbarErrors {
    float: left;
    margin: 7px 0px 0 5px;
    font-size: 11px;
}

.fbErrors {
    padding-left: 20px;
    height: 14px;
    background: url(errorIcon.png) no-repeat;
    color: #f00;
    font-weight: bold;    
}

#fbMiniErrors {
    display: inline;
    display: none;
    float: right;
    margin: 5px 2px 0 5px;
}

#fbMiniIcon {
    float: right;
    margin: 3px 4px 0;
    height: 20px;
    width: 20px;
    float: right;    
    background: url(sprite.png) 0 -135px;
    cursor: pointer;
}


/************************************************************************************************
 Master Layout
*************************************************************************************************/
#fbChrome {
    position: fixed;
    overflow: hidden;
    height: 100%;
    width: 100%;
    border-collapse: collapse;
    background: #fff;
}

#fbTop {
    height: 49px;
}

#fbToolbar {
    position: absolute;
    z-index: 5;
    width: 100%;
    top: 0;
    background: url(sprite.png) #f1f2ee 0 0;
    height: 27px;
    font-size: 11px;
    overflow: hidden;
}

#fbPanelBarBox {
    top: 27px;
    position: absolute;
    z-index: 8;
    width: 100%;
    background: url(sprite.png) #dbd9c9 0 -27px;
    height: 22px;
}

#fbContent {
    height: 100%;
    vertical-align: top;
}

#fbBottom {
    height: 18px;
    background: #fff;
}

/************************************************************************************************
 Sub-Layout 
*************************************************************************************************/

/* fbToolbar 
*************************************************************************************************/
#fbToolbarIcon {
    float: left;
    padding: 4px 5px 0;
}

#fbToolbarIcon a {
    display: block;
    height: 20px;
    width: 20px;
    background: url(sprite.png) 0 -135px;
    text-decoration: none;
    cursor: default;
}

#fbToolbarButtons {
    float: left;
    padding: 4px 2px 0 5px;
}

#fbToolbarButtons a {
    text-decoration: none;
    display: block;
    float: left;
    color: #000;
    padding: 4px 8px 4px;
    cursor: default;
}

#fbToolbarButtons a:hover {
    color: #333;
    padding: 3px 7px 3px;
    border: 1px solid #fff;
    border-bottom: 1px solid #bbb;
    border-right: 1px solid #bbb;
}

#fbStatusBarBox {
    position: relative;
    top: 5px;
    line-height: 19px;
    cursor: default;    
}

.fbToolbarSeparator{
    overflow: hidden;
    border: 1px solid;
    border-color: transparent #fff transparent #777;
    _border-color: #eee #fff #eee #777;
    height: 7px;
    margin: 10px 6px 0 0;
    float: left;
}

.fbStatusBar span {
    color: #808080;
    padding: 0 4px 0 0;
}

.fbStatusBar span a {
    text-decoration: none;
    color: black;
}

.fbStatusBar span a:hover {
    color: blue;
    cursor: pointer;    
}


#fbWindowButtons {
    position: absolute;
    white-space: nowrap;
    right: 0;
    top: 0;
    height: 17px;
    _width: 50px;
    padding: 5px 0 5px 5px;
    z-index: 6;
    background: url(sprite.png) #f1f2ee 0 0;
}

/* fbPanelBarBox
*************************************************************************************************/

#fbPanelBar1 {
    width: 255px; /* fixed width to avoid tabs breaking line */
    z-index: 8;
    left: 0;
    white-space: nowrap;
    background: url(sprite.png) #dbd9c9 0 -27px;
    position: absolute;
    left: 4px;
}

#fbPanelBar2Box {
    background: url(sprite.png) #dbd9c9 0 -27px;
    position: absolute;
    height: 22px;
    width: 300px; /* fixed width to avoid tabs breaking line */
    z-index: 9;
    right: 0;
}

#fbPanelBar2 {
    position: absolute;
    width: 290px; /* fixed width to avoid tabs breaking line */
    height: 22px;
    padding-left: 10px;
}

/* body 
*************************************************************************************************/
.fbPanel {
    display: none;
}

#fbPanelBox1, #fbPanelBox2 {
    max-height: inherit;
    height: 100%;
    font-size: 11px;
}

#fbPanelBox2 {
    background: #fff;
}

#fbPanelBox2 {
    width: 300px;
    background: #fff;
}

#fbPanel2 {
    padding-left: 6px;
    background: #fff;
}

.hide {
    overflow: hidden !important;
    position: fixed !important;
    display: none !important;
    visibility: hidden !important;
}

/* fbBottom 
*************************************************************************************************/

#fbCommand {
    height: 18px;
}

#fbCommandBox {
    position: absolute;
    width: 100%;
    height: 18px;
    bottom: 0;
    overflow: hidden;
    z-index: 9;
    background: #fff;
    border: 0;
    border-top: 1px solid #ccc;
}

#fbCommandIcon {
    position: absolute;
    color: #00f;
    top: 2px;
    left: 7px;
    display: inline;
    font: 11px Monaco, monospace;
    z-index: 10;
}

#fbCommandLine {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    border: 0;
    margin: 0;
    padding: 2px 0 2px 32px;
    font: 11px Monaco, monospace;
    z-index: 9;
}

div.fbFitHeight {
    overflow: auto;
    _position: absolute;
}


/************************************************************************************************
 Layout Controls
*************************************************************************************************/

/* fbToolbar buttons 
*************************************************************************************************/
#fbWindowButtons a {
    font-size: 1px;
    width: 16px;
    height: 16px;
    display: block;
    float: right;
    margin-right: 4px;
    text-decoration: none;
    cursor: default;
}

#fbWindow_btClose {
    background: url(sprite.png) 0 -119px;
}

#fbWindow_btClose:hover {
    background: url(sprite.png) -16px -119px;
}

#fbWindow_btDetach {
    background: url(sprite.png) -32px -119px;
}

#fbWindow_btDetach:hover {
    background: url(sprite.png) -48px -119px;
}

/* fbPanelBarBox tabs 
*************************************************************************************************/
.fbTab {
    text-decoration: none;
    display: none;
    float: left;
    width: auto;
    float: left;
    cursor: default;
    font-family: Lucida Grande, Tahoma, sans-serif;
    font-size: 11px;
    font-weight: bold;
    height: 22px;
    color: #565656;
}

.fbPanelBar span {
    display: block;
    float: left;
}

.fbPanelBar .fbTabL,.fbPanelBar .fbTabR {
    height: 22px;
    width: 8px;
}

.fbPanelBar .fbTabText {
    padding: 4px 1px 0;
}

a.fbTab:hover {
    background: url(sprite.png) 0 -73px;
}

a.fbTab:hover .fbTabL {
    background: url(sprite.png) -16px -96px;
}

a.fbTab:hover .fbTabR {
    background: url(sprite.png) -24px -96px;
}

.fbSelectedTab {
    background: url(sprite.png) #f1f2ee 0 -50px !important;
    color: #000;
}

.fbSelectedTab .fbTabL {
    background: url(sprite.png) 0 -96px !important;
}

.fbSelectedTab .fbTabR {
    background: url(sprite.png) -8px -96px !important;
}

/* splitters 
*************************************************************************************************/
#fbHSplitter {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 5px;
    overflow: hidden;
    cursor: n-resize !important;
    background: url(pixel_transparent.gif);
    z-index: 9;
}

#fbHSplitter.fbOnMovingHSplitter {
    height: 100%;
    z-index: 100;
}

.fbVSplitter {
    background: #ece9d8;
    color: #000;
    border: 1px solid #716f64;
    border-width: 0 1px;
    border-left-color: #aca899;
    width: 4px;
    cursor: e-resize;
    overflow: hidden;
    right: 294px;
    text-decoration: none;
    z-index: 9;
    position: absolute;
    height: 100%;
    top: 27px;
    _width: 6px;
}

/************************************************************************************************/
div.lineNo {
    font: 11px Monaco, monospace;
    float: left;
    display: inline;
    position: relative;
    margin: 0;
    padding: 0 5px 0 20px;
    background: #eee;
    color: #888;
    border-right: 1px solid #ccc;
    text-align: right;
}

pre.nodeCode {
    font: 11px Monaco, monospace;
    margin: 0;
    padding-left: 10px;
    overflow: hidden;
    /*
    _width: 100%;
    /**/
}

/************************************************************************************************/
.nodeControl {
    margin-top: 3px;
    margin-left: -14px;
    float: left;
    width: 9px;
    height: 9px;
    overflow: hidden;
    cursor: default;
    background: url(tree_open.gif);
    _float: none;
    _display: inline;
    _position: absolute;
}

div.nodeMaximized {
    background: url(tree_close.gif);
}

div.objectBox-element {
    padding: 1px 3px;
}
.objectBox-selector{
    cursor: default;
}

.selectedElement{
    background: highlight;
    /* background: url(roundCorner.svg); Opera */
    color: #fff !important;
}
.selectedElement span{
    color: #fff !important;
}

/* Webkit CSS Hack - bug in "highlight" named color */ 
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .selectedElement{
      background: #316AC5;
      color: #fff !important;
    }
}

/************************************************************************************************/
/************************************************************************************************/
.logRow * {
    font-size: 11px;
}

.logRow {
    position: relative;
    border-bottom: 1px solid #D7D7D7;
    padding: 2px 4px 1px 6px;
    background-color: #FFFFFF;
}

.logRow-command {
    font-family: Monaco, monospace;
    color: blue;
}

.objectBox-string,
.objectBox-text,
.objectBox-number,
.objectBox-function,
.objectLink-element,
.objectLink-textNode,
.objectLink-function,
.objectBox-stackTrace,
.objectLink-profile {
    font-family: Monaco, monospace;
}

.objectBox-null {
    padding: 0 2px;
    border: 1px solid #666666;
    background-color: #888888;
    color: #FFFFFF;
}

.objectBox-string {
    color: red;
    white-space: pre;
}

.objectBox-number {
    color: #000088;
}

.objectBox-function {
    color: DarkGreen;
}

.objectBox-object {
    color: DarkGreen;
    font-weight: bold;
    font-family: Lucida Grande, sans-serif;
}

.objectBox-array {
    color: #000;
}

/************************************************************************************************/
.logRow-info,.logRow-error,.logRow-warning {
    background: #fff no-repeat 2px 2px;
    padding-left: 20px;
    padding-bottom: 3px;
}

.logRow-info {
    background-image: url(infoIcon.png);
}

.logRow-warning {
    background-color: cyan;
    background-image: url(warningIcon.png);
}

.logRow-error {
    background-color: LightYellow;
    background-image: url(errorIcon.png);
    color: #f00;
}

.errorMessage {
    vertical-align: top;
    color: #f00;
}

.objectBox-sourceLink {
    position: absolute;
    right: 4px;
    top: 2px;
    padding-left: 8px;
    font-family: Lucida Grande, sans-serif;
    font-weight: bold;
    color: #0000FF;
}

/************************************************************************************************/
.logRow-group {
    background: #EEEEEE;
    border-bottom: none;
}

.logGroup {
    background: #EEEEEE;
}

.logGroupBox {
    margin-left: 24px;
    border-top: 1px solid #D7D7D7;
    border-left: 1px solid #D7D7D7;
}

/************************************************************************************************/
.selectorTag,.selectorId,.selectorClass {
    font-family: Monaco, monospace;
    font-weight: normal;
}

.selectorTag {
    color: #0000FF;
}

.selectorId {
    color: DarkBlue;
}

.selectorClass {
    color: red;
}

/************************************************************************************************/
.objectBox-element {
    font-family: Monaco, monospace;
    color: #000088;
}

.nodeChildren {
    padding-left: 26px;
}

.nodeTag {
    color: blue;
    cursor: pointer;
}

.nodeValue {
    color: #FF0000;
    font-weight: normal;
}

.nodeText,.nodeComment {
    margin: 0 2px;
    vertical-align: top;
}

.nodeText {
    color: #333333;
    font-family: Monaco, monospace;
}

.nodeComment {
    color: DarkGreen;
}

/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * */

.nodeHidden, .nodeHidden * {
    color: #888888;
}

.nodeHidden .nodeTag {
    color: #5F82D9;
}

.nodeHidden .nodeValue {
    color: #D86060;
}

.selectedElement .nodeHidden, .selectedElement .nodeHidden * {
    color: SkyBlue !important;
}


/************************************************************************************************/
.log-object {
    /*
    _position: relative;
    _height: 100%;
    /**/
}

.property {
    position: relative;
    clear: both;
    height: 15px;
}

.propertyNameCell {
    vertical-align: top;
    float: left;
    width: 28%;
    position: absolute;
    left: 0;
    z-index: 0;
}

.propertyValueCell {
    float: right;
    width: 68%;
    background: #fff;
    position: absolute;
    padding-left: 5px;
    display: table-cell;
    right: 0;
    z-index: 1;
    /*
    _position: relative;
    /**/
}

.propertyName {
    font-weight: bold;
}

.FirebugPopup {
    height: 100% !important;
}

.FirebugPopup #fbWindowButtons {
    display: none !important;
}

.FirebugPopup #fbHSplitter {
    display: none !important;
}
