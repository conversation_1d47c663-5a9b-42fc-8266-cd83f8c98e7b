<?php
ini_set("error_reporting","E_ALL & ~E_NOTICE");
header("Content-type: text/html;charset=utf-8");
date_Default_TimeZone_set("PRC");
#
#
$Web_title ="音乐试听外链站";//网站标题
$Web_logo  ="https://pic.rmb.bdstatic.com/bjh/3ed83165c2/240827/371e2e93d95082116cbe84755a5c4f02.gif";//网站LOGO
$Web_url   =str_replace("index.php","","//".$_SERVER["HTTP_HOST"].$_SERVER['PHP_SELF']);//主页地址，无需修改
$Err_img   ="https://mms0.baidu.com/it/u=1420768136,2841584893&fm=253";//无图提示，无需修改
$Err_warn  ="<script>alert('地址错误或者音乐不存在!');top.location.href='$Web_url'</script>";//错误提示，无需修改
#
#
#下面代码不懂的请勿修改，否则可能会出现致命错误
if(isset($_GET['v'])){
#播放页面
    $id = $_GET['v'];
    if (is_numeric($id)) {
    #视频播放页面
        $mv_link = base64_decode("aHR0cDovL20ua3Vnb3UuY29tL2FwcC9pL212LnBocD9jbWQ9MTAwJmlkPQ==").$id."&ismp3=1&ext=mp4";
        $mv_data = curl_get($mv_link);
        $mv_json = json_decode($mv_data,true);
        preg_match_all('/downurl":"(.*?)"/',$mv_data, $mv_down);
        $mv_name   = $mv_json['songname'];
        $mv_singer = $mv_json['singer'];
        $mv_icon   = str_replace("{size}","100",$mv_json['mvicon']);
        $mv_img    = Tohttps($mv_icon);
        $mv_title  = $mv_singer." - ".$mv_name;
		$mv_url    = Tohttps($mv_down[1][0]);
        $title     = $mv_title.",在线免费试听下载,".$mv_singer.",mv视频,".$mv_name."超清mp4下载,试听下载_".$Web_title;
        $mvdata = $mv_json['mvdata'];
        $tota = count($mvdata);
        $down_data = "";
        $down_a = array("le","sq","rq","hp");
        $down_b = array("流畅","标清","高清","超清");
        for($i = 0; $i < $tota; $i++){
            if($mvdata[$down_a[$i]]){
                $down_url_a = stripslashes($mvdata[$down_a[$i]]['downurl']);
                $down_url_b = stripslashes($mvdata[$down_a[$i]]['backupdownurl'][0]);
                $down_size  = number_format(($mvdata[$down_a[$i]]['filesize'] / 1048576),1) . " MB";
                $down_data .= "<p style=\"padding:5px;\">".$down_b[$i]."<button style=\"background-color:#fff;width:83px;text-align:right;\">(".$down_size.")</button>:<a href=\"".$down_url_a."\" target=\"_mp34\" class=\"btn\"><i class=\"fa fa-download\" aria-hidden=\"true\"></i>&nbsp;电信线路</a><a href=\"".$down_url_b."\" target=\"_mp34\" class=\"btn\"><i class=\"fa fa-download\" aria-hidden=\"true\"></i>&nbsp;移动线路</a></p>";
            }
        }
        $main = "<script type=\"text/javascript\"> function player(url) { var frameid = Math.random(); window.webmvplayer = '<div style=\"background-color:rgb(7,126,221);padding:5px;float:right;position:absolute;left:100%;border-radius:5px;margin-left:-125px;margin-top:10px;width:100px;\"><img src=\"$Web_logo\" style=\"width:100px;\"></div><video style=\"width:100%;height:100%;\" oncontextmenu=\"return false;\" poster=\"$mv_img\" id=\"media\" controls autoplay loop><source src=\"'+url+'\" type=\"video/mp4\"></video><script >document.getElementById(\\\"media\\\").play(); window.onload = function() { parent.document.getElementById(\''+frameid+'\').height = document.body.scrollWidth*0.567+\'px\'; }; <'+'/script>'; document.write('<iframe id=\"'+frameid+'\" src=\"javascript:parent.webmvplayer;\" frameBorder=\"0\" scrolling=\"no\" width=\"100%\" frameborder=\"0\" vsspace=\"0\" hspace=\"0\" marginwidth=\"0\" marginheight=\"0\" style=\"background:url($mv_img) no-repeat center center;background-size:110%;\"></iframe>'); } </script><div class=\"common\" style=\"background-color:transparent;\"><script type=\"text/javascript\">player('".$mv_url."');</script></div><div class=\"rubric common\"><div class=\"flex\"></div><a title=\"分享到新浪微博\" href=\"".share($mv_title,$id,$mv_img)."\" target=\"_mp34\"><span class=\"btn\" style=\"background:#f60;\">分享</span></a>".$mv_title."</div><div class=\"common\" style=\"margin:10px auto;text-align:center;padding:10px 0;\">".$down_data."</div>".bang("random");
    } elseif(preg_match("/^\w{7,8}$/",$id)){
    #音乐播放页面
        $time = time();
		$arr=array("NVPh5oo715z5DIWAeQlhMDsWXXQV4hwt","appid=1014","clienttime=".$time,"clientver=20000","dfid=4XRLfS1N1MCw2uBpHL4fDQN9","encode_album_audio_id=".$id,"mid=d0b34df74b41628137be34bf2d1a7b42","platid=4","srcappid=2919","token=","userid=0","uuid=d0b34df74b41628137be34bf2d1a7b42","NVPh5oo715z5DIWAeQlhMDsWXXQV4hwt");
		$signature =md5(join("",$arr));
		$link=base64_decode("aHR0cHM6Ly93d3dhcGkua3Vnb3UuY29tL3BsYXkvc29uZ2luZm8=")."?srcappid=2919&clientver=20000&clienttime=".$time."&mid=d0b34df74b41628137be34bf2d1a7b42&uuid=d0b34df74b41628137be34bf2d1a7b42&dfid=4XRLfS1N1MCw2uBpHL4fDQN9&appid=1014&platid=4&encode_album_audio_id=".$id."&token=&signature=".$signature."&userid=0";
        $data = curl_get($link);
        $json = json_decode($data,true);
		$lrctime = "[00:00.00]";#歌词初始时间
		if(empty($json['status'])) {
			$link_1 = "https://www.kugou.com/mixsong/".$id.".html";
			$data_1 = curl_get($link_1);
			preg_match('/dataFromSmarty = \[(.*?)\],/',$data_1,$s);
			preg_match('/class="albumImg">.*?<img\s+src="(?<src>[^"]+)"/is',$data_1,$a);
			$json_1 = json_decode($s[1],true);
			$audio_name = $json_1['audio_name'];#歌曲全名
			$song_name  = $json_1['song_name'];#歌名
			$singerName = $json_1['author_name'];#歌手
			$song_img   = $a[1] ? Tohttps($a[1]) : $Err_img;
			$link_2     = base64_decode("aHR0cDovL20ua3Vnb3UuY29tL2FwcC9pL2dldFNvbmdJbmZvLnBocD9jbWQ9cGxheUluZm8maGFzaD0=").$json_1['hash'];
			$data_2     = curl_get($link_2);
			$json_2     = json_decode($data_2,true);
			$song_url   = $json_2['url'];
			$krc_link   = base64_decode("aHR0cDovL20ua3Vnb3UuY29tL2FwcC9pL2tyYy5waHA/Y21kPTEwMCZ0aW1lbGVuZ3RoPTU5MjMwNzU2Jmhhc2g9").$json_1['hash'];
			$lrc        = curl_get($krc_link);
		}else{
			$song_url   = $json['data']['play_url'];
			$lrc        = $json['data']['lyrics'];
			$audio_name = $json['data']['audio_name'];#歌曲全名
			$nametaye   = preg_split('[ - ]',$audio_name);
			$song_name  = $json['data']['song_name']?$json['data']['song_name']:$nametaye[1];
			$singerName = $json['data']['author_name']?$json['data']['author_name']:$nametaye[0];
			$song_img   = $json['data']['img'] ? Tohttps($json['data']['img']) : $Err_img;
			$is_free    = $json['data']['is_free_part'];#是否为付费歌曲
		}
        $title = $audio_name.",在线免费试听,歌手[".$singerName."],音乐[".$song_name."]歌词,mp3下载,[".$singerName."]专辑,".$song_name."(单曲),免费下载_".$Web_title;
        $display = "display:none;";
		$th_lrc = preg_replace(array('/\[\D*:.*?\]\r\n/','/\[.+?].+?(：|:).+?\r\n/','/\[.+?(酷狗|-----|翻唱|翻录|版权|授权|著作权|推广|未经许可|本首歌).+?\r\n/','/\[00:00.00].+?\r\n/'),'',$lrc);
		$th_krc = $lrctime.$Web_title."\\n".str_replace(array("\r\n","\r","\n"),"\\n",$th_lrc);
		$krc = $lrc?$th_krc:$lrctime."暂无歌词";
		if((empty($song_url))||($is_free == "1")) {$song_url = Soapi_mp3($singerName,$song_name);}
        if(empty($song_url)){ $refresh_time = isset($_COOKIE[$id]) ? intval($_COOKIE[$id]) : 0; if ($refresh_time < 1){ $k = $refresh_time + 1; setcookie($id,$k); header("Refresh:0"); }else{ $krc = $lrctime; $display = ""; } }
        $main = '<script type="text/javascript">var songkrc ="'.$krc.'";function parseLyric(text) { var lines = text.split(\'\n\'),pattern = /\[\d{2}:\d{2}.\d{2}\]/g,result = []; while (!pattern.test(lines[0])) {lines = lines.slice(1); }; lines[lines.length - 1].length === 0 && lines.pop(); lines.forEach(function(v,i,a) { var time = v.match(pattern), value = v.replace(pattern,\'\'); time.forEach(function(v1,i1,a1){ var t = v1.slice(1, -1).split(\':\'); result.push([parseInt(t[0],10) * 60 + parseFloat(t[1]), value]); }); }); result.sort(function(a, b){ return a[0] - b[0];}); return result;}</script><div class="Topimg common"><div class="img_border"><img src="'.$song_img.'" id="aplay"></div></div><div class="rubric common"><div class="flex"></div><a title="分享到新浪微博" href="'.share($audio_name,$id,$song_img).'" target="_mp34"><span class="btn" style="background:#f60;">分享</span></a>'.$audio_name.'</div><div class="audioplay common"><div style="padding:10px;"><audio src="'.$song_url.'" id="audio" oncontextmenu="return false;" controls loop autoplay><embed src="'.$song_url.'" loop="true" autostart="true"></embed></audio></div><div id="musickrc"><span id="musickrc01" style="color:red;"><a target="_mp34" href="?mp3='.$song_name.'"><span style="padding:1px 8px;border-radius:3px;background:#f60;color:#fff;'.$display.'">该歌曲暂时不能播放，请点搜索试试！</span></a></span><span id="musickrc02"></span></div></div><script type="text/javascript">var my_audio = document.getElementById("audio");my_audio.onended = function(){document.getElementById("aplay").className="";};my_audio.onplaying = function(){document.getElementById("aplay").className="z360z";};my_audio.onpause = function(){document.getElementById("aplay").className="";};var lyric = parseLyric(songkrc); my_audio.ontimeupdate = function () { for (var i = 0; i < lyric.length; i++) { if(this.currentTime > lyric[i][0]){ document.getElementById("musickrc01").innerHTML = lyric[i][1]; if(i+1==lyric.length){ document.getElementById("musickrc02").innerHTML = "";}else{ document.getElementById("musickrc02").innerHTML = lyric[i + 1][1];};};};}; my_audio.play();</script>'.bang("random","1.12.1");
    }else{
        exit($Err_warn);
    }
}elseif(isset($_GET['l'])){
#音乐列表页面
    $l=$_GET['l'];
    if($l=="1"){
        $Paging_title = "网络红歌,最新最好听的网络歌曲！";
        $Paging_url   = "https://mobilecdn.kugou.com/api/v3/rank/song?pagesize=50&rankid=23784&page=1";
        $Paging_api   = "https://www.kugou.com/yy/rank/home/<USER>";
    }elseif($l=="2"){
        $Paging_title = "TOP排行榜,让音乐更贴近你的生活！";
        $Paging_url   = "https://mobilecdn.kugou.com/api/v3/rank/song?pagesize=50&rankid=8888&page=1";
        $Paging_api   = "https://www.kugou.com/yy/rank/home/<USER>";
    }elseif($l=="3"){
        $Paging_title = "抖音热歌榜";
        $Paging_url   = "https://mobilecdn.kugou.com/api/v3/rank/song?pagesize=50&rankid=52144&page=1";
        $Paging_api   = "https://www.kugou.com/yy/rank/home/<USER>";
    }elseif($l=="4"){
        $Paging_title = "恋爱的歌,让爱情变得更加甜美！";
        $Paging_url   = "https://mobilecdn.kugou.com/api/v3/rank/song?pagesize=50&rankid=67&page=1";
        $Paging_api   = "https://www.kugou.com/yy/rank/home/<USER>";
    }elseif(preg_match("/^\d{6,8}$/",$l)){
        $Paging_url   = "https://www.kugou.com/yy/special/single/".$l.".html";
    }elseif($l=="random"){
        header('Location:?l='.mt_rand(3451454,6668888));
        die;
    }else{
        exit($Err_warn);
    }
    $data = curl_get($Paging_url);
    if($l>"10"){
        preg_match('/var data=(.*?),(.*)$/mU',$data,$dddd);
        preg_match('/var specialInfo =(.*?);$/mU',$data,$Info);
        if(empty($dddd[1])){ 
            $random = isset($_COOKIE['random']) ? intval($_COOKIE['random']) : 0;
            if ($random < 3){
                $k = $random + 1; setcookie('random',$k); header('Location:?l=random');
            }
            $Paging_title = "无效歌单";
            $img = "";
            $count_json = 0;
            $txt = "<a class=\"btn\" href=\"?l=random\">随机更换</a>";
        }else{
            setcookie("random", "" , time()-1);
            $json = json_decode($dddd[1],true);
            $Info = json_decode($Info[1],true);
            $count_json = count($json);
            $Paging_title = $Info['name'];
            $img = $Info['image'];
            $txt = $Info['intro'];
        }
    }else{
        $json = json_decode($data,true);
        $img = str_replace("{size}","100",$json['data']['info'][0]['album_sizable_cover']);
        $txt = curl_get("https://v1.hitokoto.cn/?c=f&encode=text");
        $Paging_data = curl_get($Paging_api);
        preg_match_all('/data-eid="(.*?)"/',$Paging_data, $eid);
        $count_json = count($eid[1]);
    }
	$txt_img = Tohttps($img);
    $title = $Paging_title." - ".$Web_title;
    $main = "<div class=\"rubric common\" style=\"height:100px;white-space:inherit;\"><div style=\"width:80px;height:80px;float:left;padding:10px;\"><img src=\"".$txt_img."\" onerror=\"this.src='".$Err_img."'\" style=\"height:80px;width:80px;border-radius:8px;\"></div><div style=\"height: 80px;line-height:20px;color:#666;width: calc(100% - 110px);font-size:13px;margin: 10px 10px 10px 0px;float:right;overflow:hidden;\">".$Paging_title."<br>".$txt."</div></div><div class=\"rubric common\"><div class=\"lr\"><div class=\"flex\"></div>歌曲列表</div></div><div class=\"songlist common\"><ul>";
    for($i = 0; $i < $count_json; $i++){
        if($l>"10"){
            $name = $json[$i]['singername']." - ".$json[$i]['songname'];
            $id = $json[$i]['encode_album_audio_id'];
            $simg = isset($json[$i]['authors'][0]['sizable_avatar'])?str_replace("{size}","100",$json[$i]['authors'][0]['sizable_avatar']):"";
            $mvhash= "";
            $duration = "";
        }else{
            $mvhash = $json['data']['info'][$i]['mvhash'];
            $name = $json['data']['info'][$i]['filename'];
            if(isset($eid[1][$i])){    $id = $eid[1][$i];    }
            $duration = date("i:s",$json['data']['info'][$i]['duration']);
            $simg = str_replace("{size}","100",$json['data']['info'][$i]['album_sizable_cover']);
        }
        $nametaye = preg_split('[ - ]',$name);
		$song_img = Tohttps($simg);
        $href = "?v=".$id;
        $mvmain= "";
        if($mvhash){
            $mvhash = $json['data']['info'][$i]['mvdata'][0]['id'];
            $mvmain= "<a target=\"_mp34\" href=\"?v=".$mvhash."\"><i class=\"fa fa-film\" aria-hidden=\"true\"></i></a>";
        }
        if($id){
            $main .= "<li><div class=\"gname lr\"><m class=\"numb lr\"><img src=\"".$song_img."\" onerror=\"this.src='".$Err_img."'\"></m><p style=\"line-height:28px;\"><a target=\"_mp34\" href=\"".$href."\" title=\"".$name."\">".$nametaye[1]."</a></p><p style=\"font-size:80%;\">".$nametaye[0]."<span style=\"padding: 0 8px;\">".$duration."</span></p></div><div class=\"gtype fr\">".$mvmain."<a target=\"_mp34\" href=\"".$href."\"><i class=\"fa fa-music\" aria-hidden=\"true\"></i></a></div></li>";
        }
    }
    $main .= "</ul></div>";
}elseif(isset($_GET['m'])){
#视频列表页面
    $p=$_GET['m'];
    if(preg_match("/^\+?[1-9][0-9]*$/",$p)){
        $title = "热门mv视频-第".$p."页-".$Web_title;
        $pnum = "105";
        if($p > $pnum){ exit($Err_warn); }
        $mvurl = base64_decode("aHR0cHM6Ly9tb2JpbGVjZG5iai5rdWdvdS5jb20vYXBpL3Y1L3ZpZGVvL2xpc3Q=")."?version=9108&plat=0&pagesize=18&id=0&sort=4&short=0&page=".$p;
        $data = curl_get($mvurl);
        $json = json_decode($data,true);
        $list = $json['data']['info'];
        if(empty($list)){ exit($Err_warn); }
        $tota = count($list);
        $main = "<div class=\"rubric common\"><div class=\"lr\"><div class=\"flex\"></div>热舞MV大放送</div><div class=\"fr\"><small>共".$pnum."页</small> <select  onchange=\"javascript:location.href=this.options[this.selectedIndex].value\"><option disabled selected hidden>第".$p."页</option>";
        if($pnum<5){ $a=1; $b=$pnum-5; }elseif($p<5){ $a=1; $b=$p+1; }elseif($p<$pnum-5){ $a=$b=$p; $pp=$p-5; $pn="-前5页-"; $main .= "<option value=\"?m=".$pp."\" >".$pn."</option>";}elseif($p<=$pnum){ $a=$b=$pnum-5; $pp=$p-5; $pn="-前5页-";$main .= "<option value=\"?m=".$pp."\" >".$pn."</option>"; }else{ exit($Err_warn); }
        for($i=$a;$i<=$b+5;$i++){
            $main .= "<option value=\"?m=".$i."\" >第".$i."页</option>";    
        }
        $main .= "</select></div></div><div class=\"Picture common\">";
        for ($i=0;$i<$tota;$i++){
            $gq = $list[$i]['title'];
            $mpic = str_replace("{size}","100",$list[$i]['img']);
			$mv_img = Tohttps($mpic);
            $hash = $list[$i]['videoid'];
            if($hash){
                $main .= "<li><a href=\"?v=".$hash."\" target=\"_mp34\" title=\"".$gq."\"><img src=\"".$mv_img."\" onerror=\"this.src='".$Err_img."'\"><span>".$gq."</span></a></li>";
            }
        }
        $main .= "</div></div></div>";
    }else{
        exit($Err_warn);
    }
}elseif((isset($_GET['mp3']))xor(isset($_GET['mp4']))){
#搜索页面
    if(empty($_GET['p'])){
        $p = "1";
    }elseif(preg_match("/^\+?[1-9][0-9]*$/",$_GET['p'])){
        $p = $_GET['p'];
    }else{
        exit($Err_warn);
    }
    if (strlen($_GET['mp3']) > 0) {
    #搜索音乐页面
        $w = htmlspecialchars($_GET['mp3']);
        $num = "30";
        $clienttime=time();
        $arr=array("NVPh5oo715z5DIWAeQlhMDsWXXQV4hwt","appid=1014","bitrate=0","clienttime=".$clienttime,"clientver=1000","dfid=4XRLfS1N1MCw2uBpHL4fDQN9","filter=10","inputtype=0","iscorrection=1","isfuzzy=0","keyword=".$w,"mid=d0b34df74b41628137be34bf2d1a7b42","page=".$p,"pagesize=".$num,"platform=WebFilter","privilege_filter=0","srcappid=2919","token=","userid=93305890","uuid=d0b34df74b41628137be34bf2d1a7b42","NVPh5oo715z5DIWAeQlhMDsWXXQV4hwt");
        $signature =md5(join("",$arr));
        $api=base64_decode("aHR0cHM6Ly9jb21wbGV4c2VhcmNoLmt1Z291LmNvbS92Mi9zZWFyY2gvc29uZz9zcmNhcHBpZD0yOTE5")."&clientver=1000&clienttime=".$clienttime."&mid=d0b34df74b41628137be34bf2d1a7b42&uuid=d0b34df74b41628137be34bf2d1a7b42&dfid=4XRLfS1N1MCw2uBpHL4fDQN9&keyword=".$w."&page=".$p."&pagesize=".$num."&bitrate=0&isfuzzy=0&inputtype=0&platform=WebFilter&userid=93305890&iscorrection=1&privilege_filter=0&filter=10&token=&appid=1014&signature=".$signature."";
        $title = "「".$w."」的mp3音乐搜索结果,".$Web_title;
        $data = curl_get($api);
        $json = json_decode($data,true);
        $nums = $json['data']['total'];
        $list = $json['data']['lists'];
        $tota = count($list);
        $pnum = ceil($nums/$num);
        $main = "<div class=\"rubric common\"><div class=\"lr\"><div class=\"flex\"></div>切换：<a style=\"margin: 0 5px;\">#音频</a><a style=\"margin: 0 5px;\" href=\"?mp4=".$w."\">#视频</a></div><div class=\"fr\"><small>共".$pnum."页</small><select  onchange=\"javascript:location.href=this.options[this.selectedIndex].value\"><option disabled selected hidden>第".$p."页</option>";
        if($pnum<5){ $a=1; $b=$pnum-5; }elseif($p<5){ $a=1; $b=$p+1; }elseif($p<$pnum-5){ $a=$b=$p; $pp=$p-5; $pn="-前5页-"; $main .= "<option value=\"?mp3=".$w."&p=".$pp."\" >".$pn."</option>";}elseif($p<=$pnum){ $a=$b=$pnum-5; $pp=$p-5; $pn="-前5页-";$main .= "<option value=\"?mp3=".$w."&p=".$pp."\" >".$pn."</option>"; }else{ exit($Err_warn); }
        for($i=$a;$i<=$b+5;$i++){
            $main .= "<option value=\"?mp3=".$w."&p=".$i."\" >第".$i."页</option>";    
        }
        $singer_img = "";    
        $main .= "</select></div></div><div class=\"songlist common\"><ul>";
        for ($i=0;$i<$tota;$i++){
            $filename = $list[$i]['FileName'];
            $songname = $list[$i]['SongName'];
            $nametaye = preg_split('[ - ]',$filename);
            $audio_id = $list[$i]['EMixSongID'];
            $duration = date("i:s",$list[$i]['Duration']);
            $downsize = number_format(($list[$i]['FileSize'] / 1048576),1) . " MB";
            $main .= "<li><div class=\"gname lr\"><m class=\"numb lr\"><img src=\"".$singer_img."\" onerror=\"this.src='http://mms2.baidu.com/it/u=2378313282,3083788303&fm=253'\"></m><p style=\"line-height:28px;\"><a target=\"_mp34\" href=\"?v=".$audio_id."\" title=\"".$filename."\">".str_ireplace($w,"<font color='red'>".$w."</font>",$nametaye[1])."</a></p><p style=\"font-size:80%;\">".str_ireplace($w,"<font color='red'>".$w."</font>",$nametaye[0])."<span style=\"padding: 0 8px;\">".$downsize."</span></p></div><div class=\"gtype fr\">".$duration."</div></li>";
        }
        $main .= "</ul></div>";
    }elseif (strlen($_GET['mp4']) > 0) {
    #搜索视频页面
        $w = htmlspecialchars($_GET['mp4']);
        $api = base64_decode("aHR0cHM6Ly9tdnNlYXJjaC5rdWdvdS5jb20vbXZfc2VhcmNoP2tleXdvcmQ9").urlencode($w)."&page=".$p."&pagesize=21";
        $title = "「".$w."」的mv视频搜索结果,".$Web_title;
        $data = curl_get($api);
        $json = json_decode($data,true);
        $nums = $json['data']['total'];
        $num = $json['data']['pagesize'];
        $list = $json['data']['lists'];
        $tota = count($list);
        $pnum = ceil($nums/$num);
        $main = "<div class=\"rubric common\"><div class=\"lr\"><div class=\"flex\"></div>切换：<a style=\"margin: 0 5px;\" href=\"?mp3=".$w."\">#音频</a><a style=\"margin: 0 5px;\">#视频</a></div><div class=\"fr\"><small>共".$pnum."页</small><select  onchange=\"javascript:location.href=this.options[this.selectedIndex].value\"><option disabled selected hidden>第".$p."页</option>";
        if($pnum<5){ $a=1; $b=$pnum-5; }elseif($p<5){ $a=1; $b=$p+1; }elseif($p<$pnum-5){ $a=$b=$p; $pp=$p-5; $pn="-前5页-"; $main .= "<option value=\"?mp3=".$w."&p=".$pp."\" >".$pn."</option>";}elseif($p<=$pnum){ $a=$b=$pnum-5; $pp=$p-5; $pn="-前5页-";$main .= "<option value=\"?mp3=".$w."&p=".$pp."\" >".$pn."</option>"; }else{ exit($Err_warn); }
        for($i=$a;$i<=$b+5;$i++){
            $main .= "<option value=\"?mp4=".$w."&p=".$i."\" >第".$i."页</option>";    
        }
        $main .= "</select></div></div><div class=\"Picture common\">";
        for ($i=0;$i<$tota;$i++){
            $nnmm = $list[$i]['FileName'];
            $name = str_ireplace($w,"<font color='red'>".$w."</font>",$nnmm);
            $pppp = $list[$i]['Pic'];
            $mpic = "https://imge.kugou.com/mvhdpic/240/".substr($pppp,0,8)."/".$pppp;
            $hash = $list[$i]['MvID'];
            if($nnmm){
                $main .= "<li><a target=\"_mp34\" href=\"?v=".$hash."\" title=\"".$nnmm."\"><img src=\"".$mpic."\" onerror=\"this.src='".$Err_img."'\"><span>".$name."</span></a></li>";
            }
        }
        $main .= "</div>";
    }else{
        exit($Err_warn);
    }
}else{
#首页
    $title = $Web_title;
    $main  = special().bang("top");
}

#下面为通用函数
function special(){
    global $Err_img;
    $output = "<div class=\"rubric common\"><div class=\"lr\"><div class=\"flex\"></div>热门歌单</div><div class=\"fr\"><a href=\"?l=random\"><i class=\"fa fa-chevron-right\" aria-hidden=\"true\"></i></a></div></div><div class=\"Picture common\">";
    $link = base64_decode("aHR0cHM6Ly93d3cua3Vnb3UuY29tL3l5L3NwZWNpYWwvaW5kZXgvMS00LTAuaHRtbA==");
    $data = curl_get($link);
    $data = preg_replace('#\s+#','',trim($data));
    preg_match_all('/pc_temp_bicon_share(.*?)<\/a>/is', $data, $last);
    $total = count($last[1]);
    $shu = "10";
    $pnum = floor($total/$shu);
    $num = $pnum*$shu;
    $su = mt_rand(0,$num-10);
    $ccc = $su+10;
    for($i = $su; $i < $ccc; $i++){
        preg_match('/data-collection="(.*?)"/',$last[1][$i],$tt);
        preg_match('/data-id="(.*?)"/',$last[1][$i],$hh);
        preg_match('/data-img="(.*?)"/',$last[1][$i],$ii);
        $mingzi = $tt[1];
        $tupian = Tohttps($ii[1]);
        $id = preg_replace('/[^0-9]/', '', $hh[1]);
        if($id){
            $output .= "<li style=\"width:16.8%\"><a href=\"?l=".$id."\" title=\"".$mingzi."\"><img src=\"".$tupian."\" onerror=\"this.src='".$Err_img."'\"><span>".$mingzi."</span></a></li>";
        }
    }
    $output .= "</div>";
    return $output;
}

function Soapi_mp3($singerName,$songName){
    $mp3_url = "";
    $singer   = preg_split('/[&、]+/u',$singerName);
    $s_singer = ToDBC($singer[0]);
    $s_name   = ToDBC($songName);
    $keyword  = urlencode($s_name.' - '.$singerName);
    $link = base64_decode("aHR0cHM6Ly93d3cuaGhscWlsb25nemh1LmNuL2FwaS9kZ19rZ211c2ljLnBocD9uPTEmdHlwZT1qc29uJmdtPQ==").$keyword;
    $data = curl_get($link);
    $so_json = json_decode($data,true);
	if($so_json["code"]==200){
		$song_singer = ToDBC($so_json["singer"]);
		$song_name   = ToDBC($so_json["title"]);
		$song_title  = $song_singer.$song_name;
		if ((stripos($song_title,$s_name) !== false)and(stripos($song_title,$s_singer) !== false)) {
			$mp3_url = check_url($so_json["music_url"]);
		}

	}
    return $mp3_url;
}

function bang($class,$vesr=''){
    global $Err_img;
    if($class=="top"){
        $rankid = "6666";
    }else if($class=="random"){
        $a = array("8888","49224","49223","49225");
        $s = array_rand($a,1);
        $rankid = $a[$s];
    }
	setcookie("rankid",$rankid);
    $link = base64_decode("aHR0cHM6Ly9tb2JpbGVjZG4ua3Vnb3UuY29tL2FwaS92My9yYW5rL3Nvbmc/cGFnZXNpemU9MTAwJnJhbmtpZD0=").$rankid."&page=1";
    $rank = base64_decode("aHR0cHM6Ly93d3cua3Vnb3UuY29tL3l5L3JhbmsvaG9tZS8xLQ==").$rankid.".html";
    $data = curl_get($link);
    $json = json_decode($data,true);
    $mix_data = curl_get($rank);
    preg_match_all('/data-eid="(.*?)"/',$mix_data, $eid);
    $num = count($eid[1]);
    if($class=="top"){
        $max_mum = "20";
        $mainname = "试听榜单";
        $iii = $num>$max_mum?$max_mum:$num;
        $su = 0;
    }else if($class=="random"){
        $max_mum = "8";
        $mainname = "猜你喜欢";
        $iii = ($num-$max_mum < 0) ? 0 : $num-$max_mum;
        $su = mt_rand(0,$iii);
    }
    $k = 0;
    $main = "<div class=\"rubric common\"><div class=\"lr\"><div class=\"flex\"></div>".$mainname."</div><div class=\"fr\"><a href=\"?l=1\"><i class=\"fa fa-chevron-right\" aria-hidden=\"true\"></i></a></div></div><div class=\"songlist common\"><ul>";
    if(!empty($vesr)){ if (empty($_COOKIE["V"])){ $link = base64_decode("aHR0cHM6Ly9naXRlZS5jb20vYXRvb2wvbXAzNC9yZWxlYXNlcy9sYXRlc3Q="); $html = check_url($link);  preg_match('/tag\/v([\d\.]+)/', $html, $vers); if($vers[1]){ setcookie("V","V".$vesr."N".$vers[1]); if($vers[1]!==$vesr){ $main .="<script>alert('".base64_decode('5Y+R546w5paw54mI5pys77yBXG7mm7TmlrDlnLDlnYDvvJo=')."$link');</script>";} } } }
    if(empty($num)){
        $main .= "<li style=\"text-align:center;\">数据加载失败！请<a class=\"btn\"  href=\"javascript:void(0);\" onclick=\"location.reload();\">刷新</a></li>";
    }else{
        for($i=$su;$i<$su+$max_mum; $i++){
            $k++;
            $mvhash = "";
            $mvmain = "";
            $id = $eid[1][$i];
			$cover = str_replace("{size}","100",$json['data']['info'][$i]['album_sizable_cover']);
            $img = Tohttps($cover);
            $j = $k>9?$k:'0'.$k;
            if($id){
                $filename = $json['data']['info'][$i]['filename'];
                $duration = date("i:s",$json['data']['info'][$i]['duration']);
                $addtime = $json['data']['info'][$i]['addtime'];
                $nametaye = preg_split('[ - ]',$filename);
                $href = "?v=".$id;
                if($json['data']['info'][$i]['mvhash']){
                    $mvhash = $json['data']['info'][$i]['mvdata'][0]['id'];
                    $mvmain= "<a href=\"?v=".$mvhash."\" title=\"视频\"><i class=\"fa fa-film\" aria-hidden=\"true\"></i></a>";
                }
                $main .= "<li><div class=\"gname lr\"><m class=\"numb lr\"><img src=\"".$img."\" onerror=\"this.src='".$Err_img."'\"></m><p style=\"line-height:28px;\"><a href=\"".$href."\" target=\"_mp34\" title=\"".$filename."\">".$nametaye[1]."</a></p><p style=\"font-size:80%;\">".$nametaye[0]."<span style=\"padding: 0 8px;\">".$duration."</span></p></div><div class=\"gtype fr\">".$mvmain."<a href=\"".$href."\" title=\"音乐\"><i class=\"fa fa-music\" aria-hidden=\"true\"></i></a></div></li>";
            }
        }
    }
    $main .= "</ul></div>";
    return $main;
}

function share($title,$hash,$mv_img){
    global $Web_url;
    $share=base64_decode("aHR0cHM6Ly9zZXJ2aWNlLndlaWJvLmNvbS9zaGFyZS9zaGFyZS5waHA/dXJsPWh0dHAlM0E=").urlencode($Web_url)."%3fv%3d".$hash."&pic=".urlencode($mv_img)."&title=%E6%88%91%E6%AD%A3%E5%9C%A8%E8%AF%95%E5%90%AC%E3%80%90".urlencode($title)."%E3%80%91%ef%bc%8c%e6%84%9f%e8%a7%89%e4%b8%8d%e9%94%99%e5%b0%b1%e8%bd%ac%e5%8f%91%e7%82%b9%e8%b5%9e%e5%91%97%ef%bc%81&ralateUid=1797430134";
    return $share;
}
function curl_get($url){
	$temp = parse_url($url);
	$Host = $temp['host'];
	$Referer = $temp['scheme']."://".$Host.$temp['path'];
	$array1  = array ("Host:{$Host}","Origin:{$Referer}","Referer:{$Referer}");
	$array2  = [];
	if (stripos($Host,'kugou.com') !== false) {
		$array2=array("Cookie:kg_mid=d0b34df74b41628137be34bf2d1a7b42;kg_dfid=4XRLfS1N1MCw2uBpHL4fDQN9;kg_dfid_collect=d41d8cd98f00b204e9800998ecf8427e;musicwo17=kugou;","CLIENT-IP:*********","X-FORWARDED-FOR:*********",);
	}
	$header = array_merge($array1, $array2);
    $ch = curl_init();
	curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
	curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
	curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate, br');
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    $result = curl_exec($ch);
    curl_close($ch);
 	if ($result == NULL) {
		return "";
 	}
	return $result;
}
function check_url($url) {
    $curl = curl_init($url);
    curl_setopt($curl, CURLOPT_NOBODY, true);
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    $result = curl_exec($curl);
    $found = "";
    if ($result !== false) {
        $status = curl_getinfo($curl);
        if ($status["http_code"] < '400') {
            if ($status["redirect_url"]) {
                $found = $status["redirect_url"];
            }else if($status["url"]){
                $found = $status["url"];
            }
        }
    }
    curl_close($curl);
    return $found;
}
function ToDBC($txt){
    $txt=trim($txt);
    $txt=str_replace("（","(",$txt);
    $txt=str_replace("）",")",$txt);
    $txt=preg_replace(array('/&nbsp;/','/&amp;/','/&apos;/','/\p{Z}+/u','/&/','/_/','/、/','/…/','/\(.*\)/','/\[.*\]/'),'',$txt);
    return $txt;
}
function Tohttps($txt){
    $txt=str_replace("http://","//",$txt);
    return $txt;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
<title><?php echo $title?></title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<meta name="format-detection" content="telephone=no" />
<meta name="keywords" content="<?php echo $title?>,QQ空间背景音乐,音乐外链,歌曲外链,mp3外链,视频外链,背景音乐链接,外链,音乐外链网" />
<meta name="description" content="<?php echo $title?>,免费提供音频试听，可以解析和下载无版权、付费、各种不能听的歌曲，免去你寻找歌曲链接地址的苦恼。" />
<link rel="shortcut icon" href="https://m.kugou.com/favicon.ico">
<link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
<style type="text/css">
*{margin:0;border:0;list-style-type:none;}a{text-decoration:none;cursor:pointer;color:#333;}.fr{float:right;margin-right:10px;}.lr{float:left;}.common{max-width:640px;min-width:320px;margin:0px auto;background-color:#fff;}body{background-color:#eee}.btn{padding:1px 5px;border-radius:5px;background-color:#0795FA;color:#fff;height:23px;margin:0 5px;}select{height:25px;border-radius:5px;background-color:#0795FA;color:#fff;}small{margin:0 5px;}.new_top,.navbar,.menu-data{background-color:#077edd;background:linear-gradient(#0896fc,#077edd);}.flex{width:5px;height:50px;margin-right:5px;float:left;background-color:#0795FA;}.header{margin-bottom:10px;}.header .new_top{height:45px;}.header .logo{margin-left:1%;width:49%;margin-top:3px;}.header .logo img{height:40px;}.header .sousuobox{float:right;width:49%;padding:9px 0;}.header .sousuobox #keyword{float:left;height:25px;line-height:25px;text-indent:10px;border-radius:25px 0 0 25px;width:70%;outline:0;}.header .sousuobox #serch{float:left;height:27px;background:#0273c7;border-radius:0 26px 26px 0;cursor:pointer;color:#fff;}.header .sousuobox form{float:right;}.rubric{height:50px;line-height:50px;text-align:left;margin-top:10px;margin-bottom:10px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;}.songlist{margin:10px auto;}.songlist ul{padding:0;}.songlist ul li{border-bottom: 1px solid #eee;height:70px;overflow:hidden;color:#627ad3;}.songlist i{color:#5888D0;margin:0 7%;}.songlist .numb{margin-right:2%;height:52px;margin-left:0;}.songlist .numb img{border-radius:8px;height:50px;width:50px;border:1px solid #eee;}.songlist .gname{width:calc(85% - 10px);padding:10px 0;height:50px;margin-left:10px;}.songlist .gname p{white-space:nowrap;text-overflow:ellipsis;overflow:hidden;}.songlist .gname a{color:#044CB8;}.songlist .gname a:hover{color:#FA3B00;}.songlist .gtype{width:calc(15% - 10px);height:70px;line-height:70px;text-align:right;cursor:default;}.songlist .h1{border-bottom:1px solid #D9DEE1;color:#FA3B00;padding:10px;}.Topimg{background-color:transparent;}.Topimg .img_border{width:155px;height:155px;margin:10px auto;}.Topimg #aplay{border:10px solid #2A2B2D;border-radius:100px;display:inline-block;height:130px;}.Topimg img{width:130px;height:130px;border-radius:120px;}.Topimg .z360z{-webkit-animation:rotating 10s linear infinite;animation:rotating 10s linear infinite}@-webkit-keyframes rotating{from{-webkit-transform:rotate(0);-moz-transform:rotate(0);-ms-transform:rotate(0);-o-transform:rotate(0);transform:rotate(0)}to{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes rotating{from{-webkit-transform:rotate(0);-moz-transform:rotate(0);-ms-transform:rotate(0);-o-transform:rotate(0);transform:rotate(0)}to{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}.audioplay #musickrc{line-height:30px;padding:0px 10px 10px;animation: slideDown 0.3s var(--animation-curve);} .audioplay span{display:block;text-align:center;overflow:hidden;color:#C7C7C7;} .audioplay #audio{width:100%;}.Picture{overflow:hidden;clear:both;margin:10px auto;font-size:13px;}.Picture a{color:#044CB8;}.Picture a:hover{color:#ff0000;}.Picture li{display:inline-block;width:30%;margin:10px 0 10px 2.6%;}.Picture span{float:left;width:100%;overflow:hidden;height:34px;max-height:34px;line-height:17px;}.Picture img{width:100%;min-height:54px;max-height:108px;border-radius:10px;border:1px solid #eee;transition: all 0.6s;}.Picture a:hover img {filter:brightness(0.5);}.bottom {text-align:center;font-size:13px;margin-bottom:55px;}.bottom .describe{padding:10px;}.float_navbar {overflow:hidden;height:45px;position:fixed;bottom:0;width:100%;}.float_navbar .a {float:left;color:#fff;text-align:center;width:33.3%;}.navbar{height:45px;line-height: 45px;}.menu{position:relative;display:inline-block;} .menubtn{cursor:pointer;} .menu-data{position:fixed;display:none;bottom:45px;width:100%;margin:auto;left:0;right:0;}.menu-data ul{padding:0;}.menu-data li{width:33.3%;float:left;line-height:35px;}.menu-data a{color:#fff;}.menu:hover .menu-data{display: block;}
</style>
</head>
<body>
<div class="header common">
<!--[if IE]><div style="background:rgb(255,255,225);text-align:center;color:#333;padding:2px 20px;font-size:14px;overflow:hidden;height:40px;line-height:40px;">温馨提示：您的浏览器版本过低，请您升级至更快速安全的Chrome内核浏览器！<a style="text-decoration:none;color:#f00;" href="https://jifendownload.2345.cn/jifen_2345/p8_ki6x_v2.0.exe">点击下载最新版安全浏览器</a></div><![endif]-->
	<div class="new_top">
		<div class="logo lr">
			<a href="<?php echo $Web_url;?>" title="<?php echo $Web_title;?>"><img src="<?php echo $Web_logo;?>"/></a>
		</div>
		<div class="sousuobox">
			<form method="get" name="get_key" onsubmit="return getkey();"> 
				<input type="text" name="mp3" id="keyword" placeholder="输入关键字搜索"/>
				<input type="submit" id="serch" value="搜索"></input>
			</form>
		</div>
	</div>
</div>
<?php echo $main;?>

<div class="bottom common">
	<div class="describe">
		<p>声明：站内音频来自搜索引擎，本站只提供数据查询服务，不提供任何音频存储和贩卖服务。</p>
		<p><script type="text/javascript">var datatime=new Date(); document.write("&copy;2010-"+datatime.getFullYear()+".");</script><?php echo $Web_title;?></p>
	</div>
</div>
<div class="float_navbar">
	<div class="navbar common">
	<a href="<?php echo $Web_url;?>" class="a"><i class="fa fa-home"></i>&nbsp;首页</a>
	<div class="menu a">
		<p class="menubtn"><i class="fa fa-list"></i>&nbsp;菜单</p>
			<div class="menu-data common">
				<ul>
					<li><a href="?l=1">网络红歌</a></li>
					<li><a href="?l=2">Top 榜单</a></li>
					<li><a href="?l=3">抖音热歌</a></li>
					<li><a href="?l=4">恋爱的歌</a></li>
					<li><a href="?l=random">随机歌单</a></li>
					<li><a href="https://gitee.com/atool/mp34/releases/latest" target="_blank" style="color:#ffa641;">源码下载</a></li>
				</ul>
			</div>
		</div>
	<a href="?m=1" class="a"><i class="fa fa-film"></i>&nbsp;视频</a>
	</div>
</div>
<script type="text/javascript">function getkey(){var str=get_key.mp3.value;if(str.trim().length==0){ alert("请输入关键词！");get_key.mp3.focus();return false;} } console.log("\n %c \u97f3\u4e50\u5916\u94fe\u7f51\u6e90\u7801\u4e0b\u8f7d %c https://gitee.com/atool/mp34/ \n","color: #ffffff; background: #1690ff; padding:5px 0;","background: #fff; padding:5px 0;");</script>
<p style="display:none">
<script charset="UTF-8" id="LA_COLLECT" src="https://sdk.51.la/js-sdk-pro.min.js"></script><script>LA.init({id:"JT0Q4UWJMqs83KQU",ck:"JT0Q4UWJMqs83KQU"});</script>
</p>
</body>
</html>