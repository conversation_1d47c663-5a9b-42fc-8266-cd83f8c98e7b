<?php
/** article list */
function get_articles($cate_id = 0, $top_num = 10, $is_best = false, $field = 'ctime', $order = 'desc') {
	global $DB;
	
	$where = "a.art_status=3 AND c.cate_mod='article'";
	if (!in_array($field, array('views', 'ctime'))) $field = 'ctime';
	if ($cate_id > 0) {
		$cate = get_one_category($cate_id);
		if (!empty($cate)) $where .= " AND a.cate_id IN ('".$cate['cate_arrchildid']."')";
	}
	if ($is_best == true) $where .= " AND a.art_isbest=1";
	switch ($field) {
		case 'views' :
			$sortby = "a.art_views";
			break;
		case 'ctime' :
			$sortby = "a.art_ctime";
			break;
		default :
			$sortby = "a.art_ctime";
			break;
	}
	$order = strtoupper($order);
	
	$sql = "SELECT a.art_id, a.art_title, a.art_tags, a.art_intro, a.art_views, a.art_ctime, a.art_utime, c.cate_id, c.cate_mod, c.cate_name, c.cate_dir FROM ".$DB->table('articles')." a LEFT JOIN ".$DB->table('categories')." c ON a.cate_id=c.cate_id WHERE $where ORDER BY $sortby $order LIMIT $top_num";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['art_link'] = get_article_url($row['art_id']);
		$row['art_tags'] = get_format_tags($row['art_tags']);

		// 保存原始时间戳用于判断是否为当天
		$original_ctime = $row['art_ctime'];
		$row['art_ctime'] = date('Y-m-d', $row['art_ctime']);
		$row['art_utime'] = date('Y-m-d', isset($row['art_utime']) ? $row['art_utime'] : $original_ctime);
		$row['cate_link'] = get_category_url($row['cate_mod'], $row['cate_id']);

		// 判断是否为当天发表的文章
		$row['is_today'] = (date('Y-m-d', $original_ctime) == date('Y-m-d'));

		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);
	
	return $results;
}

/** article list */
function get_article_list($where = 1, $field = 'ctime', $order = 'DESC', $start = 0, $pagesize = 0) {
	global $DB;
	
	if (!in_array($field, array('views', 'ctime'))) $field = 'ctime';
	switch ($field) {
		case 'views' :
			$sortby = "a.art_views";
			break;
		case 'ctime' :
			$sortby = "a.art_ctime";
			break;
		default :
			$sortby = "a.art_ctime";
			break;
	}
	$order = strtoupper($order);
	$sql = "SELECT a.art_id, a.art_title, a.art_tags, a.art_intro, a.art_views, a.art_istop, a.art_isbest, a.art_status, a.art_ctime, c.cate_id, c.cate_name FROM ".$DB->table('articles')." a LEFT JOIN ".$DB->table('categories')." c ON a.cate_id=c.cate_id WHERE $where ORDER BY a.art_istop DESC, $sortby $order LIMIT $start, $pagesize";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		switch ($row['art_status']) {
			case 1 :
				$art_status = '<font color="#333333">草稿</font>';
				break;
			case 2 :
				$art_status = '<font color="#ff3300">待审核</font>';
				break;
			case 3 :
				$art_status = '<font color="#008800">已审核</font>';
				break;
		}
		$art_istop = $row['art_istop'] > 0 ? '<font color="#ff0000">置顶</font>' : '<font color="#cccccc">置顶</font>';
		$art_isbest = $row['art_isbest'] > 0 ? '<font color="#ff3300">推荐</font>' : '<font color="#cccccc">推荐</font>';
		$row['art_attr'] = $art_istop.' - '.$art_isbest.' - '.$art_status;
		$row['art_link'] = get_article_url($row['art_id']);
		// 只在非后台管理页面时格式化标签
		if (!defined('IN_ADMIN') && function_exists('get_format_tags')) {
			$row['art_tags'] = get_format_tags($row['art_tags']);
		}

		// 保存原始时间戳用于判断是否为当天
		$original_ctime = $row['art_ctime'];
		$row['art_ctime'] = date('Y-m-d', $row['art_ctime']);
		$row['art_utime'] = date('Y-m-d', isset($row['art_utime']) ? $row['art_utime'] : $original_ctime);

		// 判断是否为当天发表的文章
		$row['is_today'] = (date('Y-m-d', $original_ctime) == date('Y-m-d'));

		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);
		
	return $results;
}
	
/** one article */
function get_one_article($where = 1) {
	global $DB;
	
	$row = $DB->fetch_one("SELECT a.user_id, a.cate_id, a.art_id, a.art_title, a.art_tags, a.copy_from, a.copy_url, a.art_intro, a.art_content, a.art_views, a.art_istop, a.art_isbest, a.art_status, a.art_ctime, a.art_utime, c.cate_id, c.cate_name FROM ".$DB->table('articles')." a LEFT JOIN ".$DB->table('categories')." c ON a.cate_id=c.cate_id WHERE $where LIMIT 1");
	
	return $row;
}

/** prev article */
function get_prev_article($aid = 0) {
	global $DB;
	
	$row = $DB->fetch_one("SELECT art_id, art_title FROM ".$DB->table('articles')." WHERE art_status=3 AND art_id < $aid ORDER BY art_id DESC LIMIT 1");
	if (!empty($row)) {
		$row['art_link'] = get_article_url($row['art_id']);
	}
	
	return $row;
}

/** next article */
function get_next_article($aid = 0) {
	global $DB;
	
	$row = $DB->fetch_one("SELECT art_id, art_title FROM ".$DB->table('articles')." WHERE art_status=3 AND art_id > $aid ORDER BY art_id ASC LIMIT 1");
	if (!empty($row)) {
		$row['art_link'] = get_article_url($row['art_id']);
	}
	
	return $row;
}

/** rssfeed */
function get_article_rssfeed($cate_id = 0) {
	global $DB, $options;
		
	$where = "a.art_status=3 AND c.cate_mod='article'";
	$cate = get_one_category($cate_id);
	if (!empty($cate)) {
		if ($cate['cate_childcount'] > 0) {
			$where .= " AND a.cate_id IN (".$cate['cate_arrchildid'].")";
		} else {
			$where .= " AND a.cate_id=$cate_id";
		}
	}

	$sql = "SELECT a.art_id, a.cate_id, a.art_title, a.art_intro, a.art_ctime, c.cate_name FROM ".$DB->table('articles')." a LEFT JOIN ".$DB->table('categories')." c ON a.cate_id=c.cate_id";
	$sql .= " WHERE $where ORDER BY a.art_id DESC LIMIT 50";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['art_link'] = str_replace('&', '&amp;', get_article_url($row['art_id'], true));
		$row['art_intro'] = htmlspecialchars(strip_tags($row['art_intro']));
		$row['art_ctime'] = date('Y-m-d H:i:s', $row['art_ctime']);
		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);
		
	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<rss version=\"2.0\">\n";
	echo "<channel>\n";
	echo "<title>".$options['site_name']."</title>\n";
	echo "<link>".$options['site_url']."</link>\n";
	echo "<description>".$options['site_description']."</description>\n";
	echo "<language>zh-cn</language>\n";
	echo "<copyright><!--CDATA[".$options['site_copyright']."]--></copyright>\n";
	echo "<webmaster>".$options['site_name']."</webmaster>\n";
	echo "<generator>".$options['site_name']."</generator>\n";
	echo "<image>\n";
	echo "<title>".$options['site_name']."</title>\n";
	echo "<url>".$options['site_url']."logo.gif</url>\n";
	echo "<link>".$options['site_url']."</link>\n";
	echo "<description>".$options['site_description']."</description>\n";
	echo "</image>\n";
	
	foreach ($results as $row) {
		echo "<item>\n";
		echo "<link>".$row['art_link']."</link>\n";
		echo "<title>".$row['art_title']."</title>\n";
		echo "<author>".$options['site_name']."</author>\n";
		echo "<category>".$row['cate_name']."</category>\n";
		echo "<pubDate>".$row['art_ctime']."</pubDate>\n";
		echo "<guid>".$row['art_link']."</guid>\n";
		echo "<description>".$row['art_intro']."</description>\n";
		echo "</item>\n";
	}
	echo "</channel>\n";
	echo "</rss>";
	
	unset($options, $results);
}
	
/** sitemap */
function get_article_sitemap($cate_id = 0) {
	global $DB, $options;
	
	$where = "art_status=3";
	$cate = get_one_category($cate_id);
	if (!empty($cate)) {
		if ($cate['cate_childcount'] > 0) {
			$where .= " AND cate_id IN (".$cate['cate_arrchildid'].")";
		} else {
			$where .= " AND cate_id=$cate_id";
		}
	}

	$sql = "SELECT art_id, art_ctime FROM ".$DB->table('articles');
	$sql .= " WHERE $where ORDER BY art_id DESC LIMIT 100";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['art_link'] = str_replace('&', '&amp;', get_article_url($row['art_id'], true));
		$row['art_ctime'] = date('Y-m-d H:i:s', $row['art_ctime']);
		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);
	
	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
	echo "<url>\n";
	echo "<loc>".$options['site_url']."</loc>\n";
	echo "<lastmod>".iso8601('Y-m-d\TH:i:s\Z')."</lastmod>\n";
	echo "<changefreq>always</changefreq>\n";
	echo "<priority>0.9</priority>\n";
	echo "</url>\n";
	
	$now = time();
	foreach ($results as $row) {
		$prior = 0.5;
		
		if (datediff('h', $row['art_ctime']) < 24) {
			$freq = "hourly";
			$prior = 0.8;
		} elseif (datediff('d', $row['art_ctime']) < 7) {
			$freq = "daily";
			$prior = 0.7;
		} elseif (datediff('w', $row['art_ctime']) < 4) {
			$freq = "weekly";
		} elseif (datediff('m', $row['art_ctime']) < 12) {
			$freq = "monthly";
		} else {
			$freq = "yearly";
		}
		
		echo "<url>\n";
		echo "<loc>".$row['art_link']."</loc>\n";
		echo "<lastmod>".iso8601('Y-m-d\TH:i:s\Z', $row['art_ctime'])."</lastmod>\n";
		echo "<changefreq>".$freq."</changefreq>\n";
		if ($prior != 0.5) {
			echo "<priority>".$prior."</priority>\n";
		}
		echo "</url>\n";
	}

	// 添加文章分类页面
	$categories = get_all_category();
	foreach ($categories as $cate) {
		if ($cate['cate_mod'] == 'article' && $cate['cate_postcount'] > 0) {
			$cate_url = str_replace('&', '&amp;', $options['site_url'] . get_category_url('article', $cate['cate_id']));
			echo "<url>\n";
			echo "<loc>".$cate_url."</loc>\n";
			echo "<lastmod>".iso8601('Y-m-d\TH:i:s\Z')."</lastmod>\n";
			echo "<changefreq>weekly</changefreq>\n";
			echo "<priority>0.6</priority>\n";
			echo "</url>\n";
		}
	}

	// 添加文章分类页面的分页
	foreach ($categories as $cate) {
		if ($cate['cate_mod'] == 'article' && $cate['cate_postcount'] > 10) {
			$pages = ceil($cate['cate_postcount'] / 10); // 每页10个
			for ($page = 2; $page <= min($pages, 10); $page++) { // 最多包含10页
				$page_url = str_replace('&', '&amp;', $options['site_url'] . get_category_url('article', $cate['cate_id'], $page));
				echo "<url>\n";
				echo "<loc>".$page_url."</loc>\n";
				echo "<lastmod>".iso8601('Y-m-d\TH:i:s\Z')."</lastmod>\n";
				echo "<changefreq>weekly</changefreq>\n";
				echo "<priority>0.5</priority>\n";
				echo "</url>\n";
			}
		}
	}

	echo "</urlset>";

	unset($options, $results);
}

/** sodir api */
function get_article_api($cate_id = 0, $start = 0, $pagesize = 0) {
	global $DB, $options;
		
	$where = "a.art_status=3 AND c.cate_mod='article'";
	$cate = get_one_category($cate_id);
	if (!empty($cate)) {
		if ($cate['cate_childcount'] > 0) {
			$where .= " AND a.cate_id IN (".$cate['cate_arrchildid'].")";
		} else {
			$where .= " AND a.cate_id=$cate_id";
		}
	}

	$sql = "SELECT a.art_id, a.cate_id, a.art_title, a.art_tags, a.art_intro, a.art_content, a.art_views, a.art_ctime, c.cate_name FROM ".$DB->table('articles')." a LEFT JOIN ".$DB->table('categories')." c ON a.cate_id=c.cate_id";
	$sql .= " WHERE $where ORDER BY a.art_id DESC LIMIT $start, $pagesize";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['art_link'] = str_replace('&', '&amp;', get_article_url($row['art_id'], true));
		$row['art_intro'] = htmlspecialchars(strip_tags($row['art_intro']));
		$row['art_ctime'] = date('Y-m-d H:i:s', $row['art_ctime']);
		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);
	
	$total = $DB->get_count($DB->table('articles').' a', $where);
	
	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<urlset xmlns=\"http://www.sodir.org/sitemap/\">\n";
	echo "<total>".$total."</total>";
	
	foreach ($posts as $row) {
		echo "<url>\n";
		echo "<name>".$row['art_title']."</name>\n";
		echo "<link>".$row['art_link']."</link>\n";
		echo "<tags>".$row['art_tags']."</tags>\n";
		echo "<desc>".$row['art_intro']."</desc>\n";
		echo "<content><!--CDATA[".$row['art_content']."]--></desc>\n";
		echo "<cate>".$row['cate_name']."</cate>\n";
		echo "<time>".$row['art_ctime']."</time>\n";		
		echo "</url>\n";
	}
	echo "</urlset>\n";
	
	unset($options, $results);
}

/** 获取音乐外链 */
function get_music_links($cate_id = null, $limit = 10) {
	global $DB;

	// 定义音乐相关分类ID数组
	$music_categories = array(317, 318, 319); // 音乐试听专区、流行歌曲、DJ串烧

	// 如果指定了分类ID，只搜索该分类；否则搜索所有音乐分类
	if ($cate_id !== null && is_numeric($cate_id)) {
		$category_condition = "a.cate_id=$cate_id";
	} else {
		$category_condition = "a.cate_id IN (" . implode(',', $music_categories) . ")";
	}

	// 查询指定分类下的文章：已审核的文章(art_status=3) 或 管理员的待审核文章(user_id=1且art_status=2)
	// 扩展搜索条件，不仅仅是MP3，还包括其他音乐相关内容
	$sql = "SELECT a.art_id, a.art_title, a.art_content, a.art_ctime, a.user_id, a.art_status, c.cate_name
			FROM ".$DB->table('articles')." a
			LEFT JOIN ".$DB->table('categories')." c ON a.cate_id=c.cate_id
			WHERE $category_condition
			AND (a.art_content LIKE '%.mp3%'
				OR a.art_content LIKE '%music.163.com%'
				OR a.art_content LIKE '%y.qq.com%'
				OR a.art_content LIKE '%kugou.com%'
				OR a.art_content LIKE '%audio%'
				OR a.art_content LIKE '%音乐%'
				OR a.art_content LIKE '%歌曲%'
				OR a.art_content LIKE '%DJ%'
				OR a.art_content LIKE '%串烧%')
			AND (a.art_status=3 OR (a.user_id=1 AND a.art_status=2))
			ORDER BY a.art_ctime DESC
			LIMIT " . ($limit * 3);

	$query = $DB->query($sql);
	$music_links = array();

	while ($row = $DB->fetch_array($query)) {
		// 从文章内容中提取音乐链接和标题
		$content = $row['art_content'];
		$music_data = extract_music_urls_with_titles($content);

		if (!empty($music_data)) {
			$link_counter = 1; // 使用独立的计数器确保序号连续
			foreach ($music_data as $data) {
				$url = $data['url'];
				$extracted_title = $data['title'];

				// 智能生成标题
				if (!empty($extracted_title)) {
					// 如果提取到了音乐标题，直接使用它作为主标题
					$title = $extracted_title;

					// 如果有多个链接，添加序号
					if (count($music_data) > 1) {
						$title .= ' ' . $link_counter;
					}
				} else {
					// 否则使用文章标题
					$title = $row['art_title'];

					// 如果有多个链接，添加序号
					if (count($music_data) > 1) {
						$title .= ' ' . $link_counter;
					}
				}

				// 添加分类标识
				if (!empty($row['cate_name'])) {
					$title .= ' [' . $row['cate_name'] . ']';
				}

				// 如果是管理员的待审核文章，添加标识
				if ($row['art_status'] == 2 && $row['user_id'] == 1) {
					$title .= ' [管理员待审]';
				}

				$music_links[] = array(
					'title' => $title,
					'url' => $url,
					'art_id' => $row['art_id'],
					'ctime' => $row['art_ctime'],
					'original_title' => $row['art_title'],
					'extracted_title' => $extracted_title,
					'status' => $row['art_status'],
					'category' => $row['cate_name']
				);

				$link_counter++; // 递增计数器

				// 如果已经收集够了，就停止
				if (count($music_links) >= $limit) {
					break 2;
				}
			}
		}
	}

	return array_slice($music_links, 0, $limit);
}

/** 从文本中提取MP3音乐URL */
function extract_music_urls($content) {
	$urls = array();

	// 扩展匹配模式，包括更多音乐链接类型
	$patterns = array(
		// 直接的音频文件链接
		'/https?:\/\/[^\s<>"]+\.(?:mp3|m4a|wav|flac|aac|ogg)(?:\?[^\s<>"]*)?/i',

		// 网易云音乐链接
		'/https?:\/\/music\.163\.com\/[^\s<>"]+/i',

		// QQ音乐链接
		'/https?:\/\/y\.qq\.com\/[^\s<>"]+/i',

		// 酷狗音乐链接
		'/https?:\/\/[^\s<>"]*kugou\.com\/[^\s<>"]+/i',

		// 酷我音乐链接
		'/https?:\/\/[^\s<>"]*kuwo\.cn\/[^\s<>"]+/i',

		// 其他音乐平台链接
		'/https?:\/\/[^\s<>"]*(?:xiami\.com|qianqian\.com|music\.baidu\.com)\/[^\s<>"]+/i',

		// 从HTML audio标签中提取音频链接
		'/<audio[^>]*src=["\']([^"\']+\.(?:mp3|m4a|wav|flac|aac|ogg)(?:\?[^"\']*)?)["\'][^>]*>/i',
		'/<source[^>]*src=["\']([^"\']+\.(?:mp3|m4a|wav|flac|aac|ogg)(?:\?[^"\']*)?)["\'][^>]*>/i',

		// 从HTML audio标签中提取音乐平台链接
		'/<audio[^>]*src=["\']([^"\']*(?:music\.163\.com|y\.qq\.com|kugou\.com|kuwo\.cn)[^"\']*)["\'][^>]*>/i',
		'/<source[^>]*src=["\']([^"\']*(?:music\.163\.com|y\.qq\.com|kugou\.com|kuwo\.cn)[^"\']*)["\'][^>]*>/i'
	);

	foreach ($patterns as $pattern) {
		if (preg_match_all($pattern, $content, $matches)) {
			// 对于audio和source标签，取第一个捕获组
			if (strpos($pattern, 'audio') !== false || strpos($pattern, 'source') !== false) {
				if (isset($matches[1])) {
					$urls = array_merge($urls, $matches[1]);
				}
			} else {
				$urls = array_merge($urls, $matches[0]);
			}
		}
	}

	// 清理和验证URL
	$clean_urls = array();
	foreach ($urls as $url) {
		$url = trim($url);
		// 移除HTML实体编码
		$url = html_entity_decode($url);
		// 验证URL格式，接受音频文件和音乐平台链接
		if (filter_var($url, FILTER_VALIDATE_URL) && strlen($url) > 10) {
			// 检查是否是音频文件或音乐平台链接
			if (preg_match('/\.(?:mp3|m4a|wav|flac|aac|ogg)(\?.*)?$/i', $url) ||
				preg_match('/(?:music\.163\.com|y\.qq\.com|kugou\.com)/i', $url)) {
				$clean_urls[] = $url;
			}
		}
	}

	// 去重并重新索引数组，确保键是连续的
	return array_values(array_unique($clean_urls));
}

/** 从文本中提取音乐URL和对应的标题 */
function extract_music_urls_with_titles($content) {
	$music_data = array();

	// 尝试匹配每个链接前面的中文标题
	$patterns = array(
		// 匹配中文字符后跟音频文件链接
		'/([\x{4e00}-\x{9fff}\s\-\(\)（）\[\]【】]{2,50}?)\s*[：:\-\s]*\s*(https?:\/\/[^\s<>"]+\.(?:mp3|m4a|wav|flac|aac|ogg)(?:\?[^\s<>"]*)?)/u',
		// 匹配中文字符后跟音乐平台链接
		'/([\x{4e00}-\x{9fff}\s\-\(\)（）\[\]【】]{2,50}?)\s*[：:\-\s]*\s*(https?:\/\/(?:music\.163\.com|y\.qq\.com|[^\s<>"]*kugou\.com)[^\s<>"]+)/u',
	);

	foreach ($patterns as $pattern) {
		if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
			foreach ($matches as $match) {
				if (isset($match[1]) && isset($match[2])) {
					$title = trim($match[1]);
					$url = trim($match[2]);

					if (!empty($url) && filter_var($url, FILTER_VALIDATE_URL)) {
						$music_data[] = array(
							'url' => $url,
							'title' => $title
						);
					}
				}
			}
		}
	}

	// 如果没有找到带标题的链接，尝试从JSON格式提取
	if (empty($music_data)) {
		$music_data = extract_from_json($content);
	}

	// 如果还是没有，提取纯链接
	if (empty($music_data)) {
		$urls = extract_music_urls($content);
		foreach ($urls as $url) {
			$music_data[] = array(
				'url' => $url,
				'title' => ''
			);
		}
	}

	return $music_data;
}

/** 从JSON格式中提取音乐数据 */
function extract_from_json($content) {
	$music_data = array();

	// 尝试解析完整JSON
	$json = json_decode($content, true);
	if (json_last_error() === JSON_ERROR_NONE && is_array($json)) {
		// 单个JSON对象
		if (isset($json['music']) && isset($json['desc'])) {
			$url = trim($json['music']);
			$title = extract_chinese_from_text($json['desc']);

			if (!empty($url) && filter_var($url, FILTER_VALIDATE_URL)) {
				$music_data[] = array(
					'url' => $url,
					'title' => $title
				);
			}
		}
		// JSON数组
		elseif (is_array($json)) {
			foreach ($json as $item) {
				if (is_array($item) && isset($item['music']) && isset($item['desc'])) {
					$url = trim($item['music']);
					$title = extract_chinese_from_text($item['desc']);

					if (!empty($url) && filter_var($url, FILTER_VALIDATE_URL)) {
						$music_data[] = array(
							'url' => $url,
							'title' => $title
						);
					}
				}
			}
		}
	} else {
		// 尝试匹配JSON片段
		if (preg_match_all('/\{[^{}]*"desc"\s*:\s*"([^"]+)"[^{}]*"music"\s*:\s*"([^"]+)"[^{}]*\}/i', $content, $matches, PREG_SET_ORDER)) {
			foreach ($matches as $match) {
				$title = extract_chinese_from_text($match[1]);
				$url = trim($match[2]);

				if (!empty($url) && filter_var($url, FILTER_VALIDATE_URL)) {
					$music_data[] = array(
						'url' => $url,
						'title' => $title
					);
				}
			}
		}
	}

	return $music_data;
}

/** 从文本中提取中文字符 */
function extract_chinese_from_text($text) {
	// 移除HTML标签
	$text = strip_tags($text);

	// 提取中文字符
	preg_match_all('/[\x{4e00}-\x{9fff}]+/u', $text, $matches);

	if (!empty($matches[0])) {
		// 合并中文字符，用空格分隔
		$chinese_text = implode(' ', $matches[0]);

		// 限制长度
		if (mb_strlen($chinese_text) > 50) {
			$chinese_text = mb_substr($chinese_text, 0, 47) . '...';
		}

		return trim($chinese_text);
	}

	return '';
}



/** 生成音乐播放器友好的标题 */
function format_music_title($title, $url) {
	// 从URL中提取可能的歌曲信息
	$url_parts = parse_url($url);
	$filename = basename($url_parts['path']);

	// 移除文件扩展名
	$name_without_ext = preg_replace('/\.[^.]+$/', '', $filename);

	// 如果标题太长，截取并添加省略号
	if (mb_strlen($title) > 30) {
		$title = mb_substr($title, 0, 27) . '...';
	}

	// 如果标题包含常见的音乐关键词，直接使用
	if (preg_match('/(?:音乐|歌曲|MP3|歌|曲)/i', $title)) {
		return $title;
	}

	// 否则添加音乐标识
	return $title . ' [音乐]';
}
?>