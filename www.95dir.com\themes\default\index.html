<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>{#$site_title#}</title>
    <meta name="keywords" content="{#$site_keywords#}" />
    <meta name="description" content="{#$site_description#}" />
    <meta name="author" content="95目录网" />
    <meta name="copyright" content="Powered By 95dir.com" />
    <meta name="robots" content="index,follow" />
    <meta name="googlebot" content="index,follow" />
    <meta name="baiduspider" content="index,follow" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="95目录网 - 优质网站分类目录">
    <meta name="theme-color" content="#007bff">
    <meta name="msapplication-TileColor" content="#007bff">
    <meta name="msapplication-navbutton-color" content="#007bff">

    <!-- SEO优化 - Open Graph标签 -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="{#$site_title#}" />
    <meta property="og:description" content="{#$site_description#}" />
    <meta property="og:url" content="{#$site_url#}" />
    <meta property="og:site_name" content="95目录网" />
    <meta property="og:locale" content="zh_CN" />

    <!-- SEO优化 - Twitter Card标签 -->
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content="{#$site_title#}" />
    <meta name="twitter:description" content="{#$site_description#}" />

    <!-- SEO优化 - 结构化数据 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "95目录网",
        "url": "{#$site_url#}",
        "description": "{#$site_description#}",
        "inLanguage": "zh-CN",
        "isFamilyFriendly": true,
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{#$site_url#}?mod=search&query={search_term_string}",
            "query-input": "required name=search_term_string"
        },
        "mainEntity": {
            "@type": "ItemList",
            "name": "优质网站分类目录",
            "description": "收录各行业优质网站的分类目录"
        }
    }
    </script>

    <!-- SEO优化 - 移动端友好性声明 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "MobileApplication",
        "name": "95目录网",
        "operatingSystem": "All",
        "applicationCategory": "BusinessApplication",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "CNY"
        }
    }
    </script>

    <!-- SEO优化 - 网站验证 -->
    <meta name="baidu-site-verification" content="codeva-{#$site_url|md5#}" />
    <meta name="google-site-verification" content="google-{#$site_url|md5#}" />

    <!-- SEO优化 - 规范链接 -->
    <link rel="canonical" href="{#$site_url#}" />

    <!-- SEO优化 - 网站图标 -->
    <link rel="icon" type="image/x-icon" href="{#$site_root#}favicon.ico" />
    <link rel="shortcut icon" type="image/x-icon" href="{#$site_root#}favicon.ico" />
    <link rel="apple-touch-icon" href="{#$site_root#}favicon.ico" />

    <!-- SEO优化 - DNS预解析 -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com" />
    <link rel="dns-prefetch" href="//t0.gstatic.cn" />
    <link rel="dns-prefetch" href="//www.google.com" />
    <link rel="dns-prefetch" href="//www.baidu.com" />

    <!-- SEO优化 - 预连接重要资源 -->
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin />
    <link rel="preconnect" href="https://t0.gstatic.cn" crossorigin />

    <!-- ========== 样式表 ========= -->
    <link href="{#$site_root#}themes/default/skin/nav.css" rel="stylesheet" type="text/css" />
    <link href="{#$site_root#}themes/default/skin/footer-modern.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
          integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
          crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- ========== 脚本 ========= -->
    <script type="text/javascript">
        var sitepath = '{#$site_root#}';
        var rewrite  = '{#$cfg.link_struct#}';
    </script>
    <script type="text/javascript" src="{#$site_root#}public/scripts/jquery.min.js"></script>
    <script type="text/javascript" src="{#$site_root#}public/scripts/common.js"></script>
















</head>

<body>
    <!-- SEO优化 - 结构化数据：面包屑导航 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "首页",
                "item": "{#$site_url#}"
            }
        ]
    }
    </script>

    <!-- SEO优化 - 结构化数据：组织信息 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "95目录网",
        "url": "{#$site_url#}",
        "logo": "{#$site_url#}logo.png",
        "description": "{#$site_description#}",
        "sameAs": [
            "{#$site_url#}"
        ]
    }
    </script>

{#include file="topbar.html"#}

<div id="wrapper">
    <!-- ================= 顶部区域（logo / 搜索 / 导航） ================= -->
    <header id="header" role="banner">
        <!-- ---------- LOGO + 搜索 ---------- -->
        <div id="topbox">
            <a href="{#$site_url#}" class="logo" title="{#$site_title#}" aria-label="95目录网首页"></a>

            <div id="sobox" role="search">
                <form name="sofrm" class="sofrm" method="get" action="" onSubmit="return rewrite_search()" role="search" aria-label="网站搜索">
                    <input name="mod"  type="hidden" value="search" />
                    <input name="type" type="hidden" id="type" value="name" />

                    <div id="selopt">
                        <div id="cursel">网站名称</div>
                        <ul id="options" role="listbox" aria-label="搜索类型选择">
                            <li><a href="javascript: void(0);" name="name" role="option">网站名称</a></li>
                            <li><a href="javascript: void(0);" name="url" role="option">网站地址</a></li>
                            <li><a href="javascript: void(0);" name="tags" role="option">TAG标签</a></li>
                            <li><a href="javascript: void(0);" name="intro" role="option">网站描述</a></li>
                            <li><a href="javascript: void(0);" name="article" role="option">文章关键词</a></li>
                        </ul>
                    </div>

                    <input name="query" type="text" class="sipt" id="query" placeholder="请输入搜索关键词" aria-label="搜索关键词输入框" autocomplete="off" />
                    <input type="submit" class="sbtn" value="搜 索" aria-label="执行搜索" />
                </form>
            </div>
        </div>

        <!-- ---------- 主导航 ---------- -->
        <nav id="navbox" role="navigation" aria-label="主导航菜单">
            <ul class="navbar" role="menubar">
        		<li role="none"><a href="?mod=index" role="menuitem" aria-label="返回网站首页"><i class="fas fa-home" aria-hidden="true"></i> 网站首页</a></li>
                        <li role="none"><a href="?mod=webdir" role="menuitem" aria-label="浏览网站目录"><i class="fas fa-folder" aria-hidden="true"></i> 网站目录</a></li>
                        <li role="none"><a href="?mod=vip_list" style="color: #ffd700;" role="menuitem" aria-label="查看VIP站点"><i class="fas fa-crown" aria-hidden="true"></i> VIP站点</a></li>
                        <li role="none"><a href="?mod=article" role="menuitem" aria-label="阅读站长资讯"><i class="fas fa-newspaper" aria-hidden="true"></i> 站长资讯</a></li>
                        <li role="none"><a href="?mod=weblink" role="menuitem" aria-label="友情链接交换"><i class="fas fa-link" aria-hidden="true"></i> 链接交换</a></li>
                        <li role="none"><a href="?mod=category" role="menuitem" aria-label="按分类浏览网站"><i class="fas fa-list" aria-hidden="true"></i> 分类浏览</a></li>
                        <li role="none"><a href="?mod=update" role="menuitem" aria-label="查看最新收录网站"><i class="fas fa-clock" aria-hidden="true"></i> 最新收录</a></li>
                        <li role="none"><a href="?mod=pending" role="menuitem" aria-label="查看待审核网站"><i class="fas fa-hourglass-half" aria-hidden="true"></i> 待审核</a></li>
                        <li role="none"><a href="?mod=archives" role="menuitem" aria-label="查看数据归档"><i class="fas fa-archive" aria-hidden="true"></i> 数据归档</a></li>
                        <li role="none"><a href="?mod=top" role="menuitem" aria-label="查看TOP排行榜"><i class="fas fa-trophy" aria-hidden="true"></i> TOP排行榜</a></li>
                        <li role="none"><a href="?mod=feedback" role="menuitem" aria-label="提交意见反馈"><i class="fas fa-comments" aria-hidden="true"></i> 意见反馈</a></li>
                        <li role="none"><a href="/wailian" role="menuitem" aria-label="外链发布工具"><i class="fas fa-anchor" aria-hidden="true"></i> 外链发布</a></li>
                        <!--<li role="none"><a href="http://my0713.com" role="menuitem"><i class="far fa-play-circle" aria-hidden="true"></i> 免费影视</a></li>-->
        	</ul>
        </nav>

        <!-- ---------- 公告与快捷链接 ---------- -->
        <div id="txtbox">
            <!-- 数据统计 -->
            <div class="count" style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; text-align: center; font-size: 14px;">
                数据统计：
                <b style="color: #008800;">{#$stat.category#}</b>个主题分类，
                <b style="color: #008800;">{#$stat.website#}</b>个优秀站点，
                <b style="color: #E94E77;">{#$stat.vip#}</b>个VIP站点，
                <b style="color: #28a745;">{#$stat.recommend#}</b>个推荐位，
                <b style="color: #ff6600;">{#$stat.apply#}</b>个待审站点，
                <b style="color: #f39c12;">{#$stat.rejected#}</b>个审核不通过，
                <b style="color: #dc3545;">{#$stat.blacklist#}</b>个黑名单，
                <b style="color: #008800;">{#$stat.article#}</b>篇站长资讯，
                <b style="color: #008800;">{#$stat.user#}</b>名优质会员
                <!-- SEO优化：链接质量统计 -->
                {#if isset($stat.linked) && isset($stat.unlinked)#}
                ，<b style="color: #28a745;">{#$stat.linked#}</b>个互链站点，
                <b style="color: #6c757d;">{#$stat.unlinked#}</b>个单向收录
                {#/if#}
            </div>
            <div class="site-notice">
                <ul id="lunbo" style="list-style-type: none; margin-top: 0px;font-size: 12px;">
                    <li>
                        <p>
                            一共收录 <b style="color: #008800;font: bold 16px Arial;">{#$stat.website#}</b> 个优秀站点，
                            其中VIP站点 <b style="color: #E94E77;font: bold 16px Arial;">{#$stat.vip#}</b> 个，
                            推荐位 <b style="color: #28a745;font: bold 16px Arial;">{#$stat.recommend#}</b> 个，
                            注册会员 <b style="color: #008800;font: bold 16px Arial;">{#$stat.user#}</b> 名，
                            <strong>提示：</strong><b style="color: #ff0000;">每日更新站点，轻松到首页</b>
                        </p>
                    </li>
                    <li>
                        <p>
                            <span style="color: #ff0000">
                                本网站目录提供网站快速收录服务，
                                <a href="tencent://message/?uin=3632094&Site=95目录分类&Menu=yes" target="_blank" rel="nofollow">vip席位30元/年（直链）推荐位10元/年（内链）</a>。
                                <strong><a href="tencent://message/?uin=3632094&Site=95目录分类&Menu=yes" target="_blank" rel="nofollow">我要上推荐</a></strong>
                                联系客服：
                                <a href="tencent://message/?uin=3632094&amp;Site=95目录分类&amp;Menu=yes" target="blank" rel="nofollow">
                                    <img border="0" alt="95目录分类" src="http://wpa.qq.com/pa?p=1:3632094:4">
                                </a>
                            </span>
                        </p>
                    </li>
                </ul>
            </div>

            <div class="link">
                <a href="{#$site_root#}?mod=quicksubmit" class="quick-submit-link"
                   style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
                          color: white; padding: 6px 12px; border-radius: 15px; text-decoration: none;
                          font-weight: bold; font-size: 12px; margin-right: 15px;
                          box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
                          transition: all 0.3s ease; display: inline-block;"
                   onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(255, 107, 53, 0.4)';"
                   onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(255, 107, 53, 0.3)';">
                    🚀 快速提交
                </a>
                快捷方式：
                <a href="{#$site_root#}member/?mod=website&act=add">网站提交</a> -
                <a href="{#$site_root#}member/?mod=article&act=add">软文投稿</a> -
                <a href="{#$site_root#}?mod=diypage&pid=1">帮助中心</a>
            </div>
        </div>
    </header>
<div class="blank10"></div>
    <!-- ================= VIP 推荐区 ================= -->
    <div id="inbox1" class="clearfix" aria-labelledby="vip-section-title">
        <h3 id="vip-section-title">
            <span class="vip-title">
                <img src="/public/images/viptq.png" width="80" height="20" alt="95目录网VIP推荐网站 - 优质网站推荐" title="VIP推荐网站标识" loading="lazy">
            </span>
            <button onclick="showDonatePopup()" class="donate-button"
                    style="float: right; background-color: #FF6600; color: #fff; border: none; padding: 6px 14px; font-size: 14px; font-weight: bold; border-radius: 4px; cursor: pointer; transition: background-color 0.3s ease; position: relative; overflow: hidden;"
                    aria-label="申请推荐位服务">
                我要上推荐 <span class="shine-effect"></span>
            </button>
        </h3>

        {#foreach from=get_websites(0, 12, true) item=quick#}
        <ul class="inlist1">
            <li style="position: relative;">
                <!-- 浏览量显示 -->
                <div class="view-count-badge" style="position: absolute; top: 2px; right: 2px; background: linear-gradient(45deg, #ff6b35, #ff8c42); color: white; font-size: 10px; font-weight: bold; padding: 2px 6px; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.2); z-index: 10;">
                    推广 {#if $quick.web_views > 0#}{#$quick.web_views#}{#else#}0{#/if#} 次
                </div>
                <a href="{#$quick.web_furl#}" target="_blank" title="VIP：{#$quick.web_name#}">
                    <img src="{#$quick.web_pic#}" width="110" height="35"
                         alt="{#$quick.web_name#}网站logo - VIP推荐网站" title="{#$quick.web_name#}网站截图预览" class="thumb" loading="lazy" />
                </a><br>
                <a href="{#$quick.web_furl#}" target="_blank" title="VIP：{#$quick.web_name#}"
                   style="text-decoration: none; color: #007bff; font-weight: bold; transition: color 0.3s;">
                    {#$quick.web_name#}
                    <svg t="1741858436151" class="icon" viewBox="0 0 2048 1024" version="1.1"
                         xmlns="http://www.w3.org/2000/svg" p-id="1496" width="18" height="18">
                        <path d="M128 0h1792c76.8 0 128 51.2 128 128v768c0 76.8-51.2 128-128 128H128C51.2 1024 0 972.8 0 896V128C0 64 51.2 0 128 0z"
                              fill="#FF6600" p-id="1497"></path>
                        <path d="M473.6 832c-25.6 0-51.2-25.6-64-76.8L192 179.2h166.4L512 576l243.2-396.8h179.2l-371.2 576c-38.4 64-64 76.8-89.6 76.8z
                                 m409.6-12.8L972.8 192h153.6l-89.6 627.2zM1856 422.4c-12.8 64-38.4 128-102.4 179.2-51.2 38.4-115.2 64-179.2 64h-268.8L1280
                                 819.2h-153.6l51.2-320h435.2c25.6 0 38.4-12.8 51.2-25.6 12.8-12.8 25.6-38.4 25.6-51.2 0-25.6 0-38.4-12.8-51.2-12.8-12.8
                                 -38.4-25.6-51.2-25.6h-435.2l128-166.4h320c64 0 128 25.6 166.4 64 51.2 64 64 128 51.2 179.2z"
                              fill="#FFFFFF" p-id="1498"></path>
                    </svg>
                </a>
            </li>
        </ul>
        {#/foreach#}
    </div>

    <!-- 打赏弹窗 -->
    <div id="donate-popup" style="display:none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div class="donate-popup-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; max-width: 500px; width: 90%;">
            <span class="close" onclick="closeDonatePopup()" style="position: absolute; top: 10px; right: 15px; font-size: 24px; cursor: pointer; color: #999;">&times;</span>
            <h3 style="color: #4A90E2; font-family: 'Arial', sans-serif; font-size: 22px; text-align: center; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2); margin-top: 0;">
                推荐服务价格表
            </h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <p style="margin: 0; line-height: 1.6; text-align: center;">
                    <strong style="color: #28a745;">10元上推荐位</strong> - 首页推荐展示<br>
                    <strong style="color: #E94E77;">VIP直链席位30元/每年</strong> - 顶部VIP位置<br>
                    <strong style="color: #ff6600;">5元快审服务</strong> - 1-3个工作日审核
                </p>
            </div>
            <p style="text-align: center; margin: 15px 0; color: #666;">
                备注格式：<strong style="color: #F39C12;">推荐/vip/快审+网址</strong>
            </p>
            <div class="donate-qr-codes" style="display: flex; justify-content: space-around; margin: 20px 0;">
                <div style="text-align: center;">
                    <h4>微信支付</h4>
                    <img src="/uploads/article/weixin.jpg" alt="95目录网微信支付二维码 - 网站收录付费服务" title="微信扫码支付网站收录费用" style="width: 150px; height: 150px;" loading="lazy">
                </div>
                <div style="text-align: center;">
                    <h4>支付宝支付</h4>
                    <img src="/uploads/article/zhifubao.jpg" alt="95目录网支付宝支付二维码 - 网站收录付费服务" title="支付宝扫码支付网站收录费用" style="width: 150px; height: 150px;" loading="lazy">
                </div>
            </div>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 20px;">
                <h4 style="margin-top: 0; color: #333;">服务说明：</h4>
                <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                    <li>推荐位：展示在首页推荐区域</li>
                    <li>VIP位：展示在顶部VIP推广区</li>
                    <li>快审：1-3个工作日完成审核</li>
                    <li>付款后请联系客服提供网站信息</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="blank10"></div>

    <!-- ================= 主体：左栏 ================= -->
    <div id="homebox">
        <div id="homebox-left">
            <!-- ========== 实用工具列表 ========== -->
            <dl id="hcatebox" class="clearfix" aria-labelledby="tools-title">
                <dt id="tools-title"><a href="/" aria-label="实用工具列表"><i class="fas fa-tools" style="color: #28a745; margin-right: 5px;" aria-hidden="true"></i>实用工具</a></dt>
                <dd>
                    <ul class="hcatelist" role="list" aria-label="实用工具链接">
                        <li><a href="https://www.12306.cn" target="_blank" rel="nofollow noopener" aria-label="12306火车票预订">火 车 票</a></li>
                        <li><a href="https://tianqi.qq.com" target="_blank" rel="nofollow noopener" aria-label="腾讯天气预报">天气预报</a></li>
                        <li><a href="https://www.rili.com.cn" target="_blank" rel="nofollow noopener" aria-label="万年历查询">万 年 历</a></li>
                        <li><a href="https://flights.ctrip.com/fuzzysearch/search" target="_blank" rel="nofollow noopener" aria-label="携程特价机票">特价机票</a></li>
                        <li><a href="http://typhoon.nmc.cn/mobile.html" target="_blank" rel="nofollow noopener" aria-label="台风路径实时查询">台风路径</a></li>
                        <li><a href="https://www.kuaidi100.com" target="_blank" rel="nofollow noopener" aria-label="快递100查询">快递查询</a></li>
                        <li><a href="https://www.8684.cn" target="_blank" rel="nofollow noopener" aria-label="8684公交线路查询">公交线路</a></li>
                        <li><a href="https://summary.jrj.com.cn/hsMarket" target="_blank" rel="nofollow noopener" aria-label="金融界股票行情">股票行情</a></li>
                        <li><a href="https://dey.11185.cn/web/#/idtoolkitAddress" target="_blank" rel="nofollow noopener" aria-label="邮政编码查询">邮编查询</a></li>
                        <li><a href="https://www.boc.cn/sourcedb/whpj/" target="_blank" rel="nofollow noopener" aria-label="中国银行外汇牌价">外汇牌价</a></li>
                        <li><a href="https://www.cwl.gov.cn/ygkj/wqkjgg/ssq/" target="_blank" rel="nofollow noopener" aria-label="福利彩票开奖结果">福利彩票</a></li>
                        <li><a href="https://bj.122.gov.cn" target="_blank" rel="nofollow noopener" aria-label="交通违章查询">违章查询</a></li>
                        <li><a href="https://map.baidu.com" target="_blank" rel="nofollow noopener" aria-label="百度地图导航">百度地图</a></li>
                        <li><a href="https://fanyi.baidu.com" target="_blank" rel="nofollow noopener" aria-label="百度在线翻译">在线翻译</a></li>
                        <li><a href="https://www.speedtest.cn" target="_blank" rel="nofollow noopener" aria-label="网络测速工具">网速测试</a></li>
                    </ul>
                </dd>
            </dl>

            <div class="blank10"></div>

            <!-- ========== 按地区搜索 ========== -->
            <dl id="hcatebox" class="clearfix" aria-labelledby="province-tags-title">
                <dt id="province-tags-title">
                    <a href="javascript:void(0);" aria-label="按地区搜索">
                        <i class="fas fa-map-marker-alt" style="color: #28a745; margin-right: 5px;" aria-hidden="true"></i>按地区搜索
                    </a>
                </dt>
                <dd>
                    <ul class="hcatelist province-tags-list" role="list" aria-label="中国省份地区标签">
                        <!-- 直辖市 -->
                        <li><a href="javascript:void(0);" class="province-tag municipality" data-province="北京" role="button" tabindex="0" aria-label="搜索北京地区网站">北京</a></li>
                        <li><a href="javascript:void(0);" class="province-tag municipality" data-province="上海" role="button" tabindex="0" aria-label="搜索上海地区网站">上海</a></li>
                        <li><a href="javascript:void(0);" class="province-tag municipality" data-province="天津" role="button" tabindex="0" aria-label="搜索天津地区网站">天津</a></li>
                        <li><a href="javascript:void(0);" class="province-tag municipality" data-province="重庆" role="button" tabindex="0" aria-label="搜索重庆地区网站">重庆</a></li>

                        <!-- 省份 -->
                        <li><a href="javascript:void(0);" class="province-tag" data-province="河北" role="button" tabindex="0" aria-label="搜索河北地区网站">河北</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="山西" role="button" tabindex="0" aria-label="搜索山西地区网站">山西</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="辽宁" role="button" tabindex="0" aria-label="搜索辽宁地区网站">辽宁</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="吉林" role="button" tabindex="0" aria-label="搜索吉林地区网站">吉林</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="黑龙江" role="button" tabindex="0" aria-label="搜索黑龙江地区网站">黑龙江</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="江苏" role="button" tabindex="0" aria-label="搜索江苏地区网站">江苏</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="浙江" role="button" tabindex="0" aria-label="搜索浙江地区网站">浙江</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="安徽" role="button" tabindex="0" aria-label="搜索安徽地区网站">安徽</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="福建" role="button" tabindex="0" aria-label="搜索福建地区网站">福建</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="江西" role="button" tabindex="0" aria-label="搜索江西地区网站">江西</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="山东" role="button" tabindex="0" aria-label="搜索山东地区网站">山东</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="河南" role="button" tabindex="0" aria-label="搜索河南地区网站">河南</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="湖北" role="button" tabindex="0" aria-label="搜索湖北地区网站">湖北</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="湖南" role="button" tabindex="0" aria-label="搜索湖南地区网站">湖南</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="广东" role="button" tabindex="0" aria-label="搜索广东地区网站">广东</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="海南" role="button" tabindex="0" aria-label="搜索海南地区网站">海南</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="四川" role="button" tabindex="0" aria-label="搜索四川地区网站">四川</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="贵州" role="button" tabindex="0" aria-label="搜索贵州地区网站">贵州</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="云南" role="button" tabindex="0" aria-label="搜索云南地区网站">云南</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="陕西" role="button" tabindex="0" aria-label="搜索陕西地区网站">陕西</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="甘肃" role="button" tabindex="0" aria-label="搜索甘肃地区网站">甘肃</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="青海" role="button" tabindex="0" aria-label="搜索青海地区网站">青海</a></li>
                        <li><a href="javascript:void(0);" class="province-tag" data-province="台湾" role="button" tabindex="0" aria-label="搜索台湾地区网站">台湾</a></li>

                        <!-- 自治区 -->
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="内蒙古" role="button" tabindex="0" aria-label="搜索内蒙古地区网站">内蒙古</a></li>
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="广西" role="button" tabindex="0" aria-label="搜索广西地区网站">广西</a></li>
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="西藏" role="button" tabindex="0" aria-label="搜索西藏地区网站">西藏</a></li>
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="宁夏" role="button" tabindex="0" aria-label="搜索宁夏地区网站">宁夏</a></li>
                        <li><a href="javascript:void(0);" class="province-tag autonomous" data-province="新疆" role="button" tabindex="0" aria-label="搜索新疆地区网站">新疆</a></li>

                        <!-- 特别行政区 -->
                        <li><a href="javascript:void(0);" class="province-tag special" data-province="香港" role="button" tabindex="0" aria-label="搜索香港地区网站">香港</a></li>
                        <li><a href="javascript:void(0);" class="province-tag special" data-province="澳门" role="button" tabindex="0" aria-label="搜索澳门地区网站">澳门</a></li>
                    </ul>
                </dd>
            </dl>

            <div class="blank10"></div>

            <!-- ========== 分类目录（动态） ========== -->
            <dl id="hcatebox" class="clearfix" aria-label="网站分类目录">
                {#foreach from=get_categories() item=cate#}
                {#if $cate.cate_mod == 'webdir'#}
                <dt><a href="{#$cate.cate_link#}" aria-label="查看{#$cate.cate_name#}分类">{#$cate.cate_name#}</a></dt>
                <dd>
                    <ul class="hcatelist" role="list" aria-label="{#$cate.cate_name#}子分类">
                        {#foreach from=get_categories($cate.cate_id) item=scate#}
                        <li><a href="{#$scate.cate_link#}" aria-label="查看{#$scate.cate_name#}网站">{#$scate.cate_name#}</a></li>
                        {#/foreach#}
                    </ul>
                </dd>
                {#/if#}
                {#/foreach#}
            </dl>

            <div class="blank10"></div>

            <!-- ========== 最新收录 ========== -->
            <div id="newbox" aria-labelledby="latest-sites-title">
                <h3 id="latest-sites-title"><i class="fas fa-plus-circle" style="color: #007bff; margin-right: 5px;" aria-hidden="true"></i>最新收录</h3>
                <ul class="newlist" role="list" aria-label="最新收录网站列表">
                    {#foreach from=get_websites(0, 14) item=new#}
                    <li>
                        <span>{#$new.web_ctime#}</span>
                        <a href="{#$new.web_link#}" title="{#$new.web_name#} - {#$new.web_url#}" aria-label="访问{#$new.web_name#}网站">
                            <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$new.web_url#}"
                                 width="18" height="18" alt="{#$new.web_name#}网站图标 - 最新收录网站" title="{#$new.web_name#}网站favicon图标" loading="lazy" />{#$new.web_name#} - <small>{#$new.web_url#}</small>
                            {#if $new.is_today#}<span class="new-icon" aria-label="今日新增">new</span>{#/if#}
                        </a>
                    </li>
                    {#/foreach#}
                </ul>
            </div>



            <!-- ========== 数据归档 ========== -->
            <div id="newbox">
                <h3><i class="fas fa-archive" style="color: #6f42c1; margin-right: 5px;"></i>数据归档</h3>
                <ul class="arcbox-list">
                    {#foreach from=get_archives() key=year item=arr#}
                    <li>
                        <b>{#$year#}年</b>
                        {#foreach from=$arr key=month item=item#}
                        <a href="javascript:void(0);"
                           class="archive-month-link {#if $year == '2025' && $month == '07'#}active{#/if#}"
                           data-year="{#$year#}"
                           data-month="{#$month#}"
                           title="{#$year#}年{#$month#}月共收录{#$item.site_count#}个优秀站点">
                            {#$month#}月
                        </a>
                        {#/foreach#}
                    </li>
                    {#/foreach#}
                </ul>

                <!-- 网站列表显示区域 - 完全按照最新收录的结构 -->
                <div class="archive-content-container">
                    <div id="archive-loading" class="archive-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                    </div>
                    <ul id="archive-list" class="newlist">
                        <!-- 网站列表将通过AJAX加载，结构与最新收录完全一致 -->
                    </ul>
                    <div id="archive-error" class="archive-error" style="display: none;">
                        加载失败，请稍后重试
                    </div>
                </div>
            </div>
        </div><!-- /homebox-left -->

        <!-- ================= 右侧区域 ================= -->
        <div id="homebox-right" role="complementary" aria-label="推荐内容和导航">
            <!-- 站长推荐 - 带选项卡切换 -->
            <div id="bestbox" aria-labelledby="recommend-title">
                <!-- 选项卡标题 -->
                <div class="tab-header">
                    <h3 id="recommend-title" class="tab-item active" data-tab="recommend">
                        <span style="color: #07c; font-weight: bold; text-shadow: 0 1px 2px rgba(255, 215, 0, 0.3); font-size: 14px;">
                            <i class="fas fa-crown" style="color: #ffd700; margin-right: 5px;" aria-hidden="true"></i>站长推荐
                        </span>
                    </h3>
                    <h3 class="tab-item" data-tab="pending">
                        <span style="color: #ff6600; font-weight: bold; text-shadow: 0 1px 2px rgba(255, 102, 0, 0.3); font-size: 14px;">
                            <i class="fas fa-clock" style="color: #ff6600; margin-right: 5px;" aria-hidden="true"></i>待审核
                        </span>
                    </h3>
                    <h3 class="tab-item" data-tab="blacklist">
                        <span style="color: #333; font-weight: bold; text-shadow: 0 1px 2px rgba(51, 51, 51, 0.3); font-size: 14px;">
                            <i class="fas fa-ban" style="color: #333; margin-right: 5px;" aria-hidden="true"></i>黑名单
                        </span>
                    </h3>
                    <h3 class="tab-item" data-tab="rejected">
                        <span style="color: #f39c12; font-weight: bold; text-shadow: 0 1px 2px rgba(243, 156, 18, 0.3); font-size: 14px;">
                            <i class="fas fa-times-circle" style="color: #f39c12; margin-right: 5px;" aria-hidden="true"></i>审核不通过
                        </span>
                    </h3>
                </div>

                <!-- 选项卡内容 -->
                <div class="tab-content">
                    <!-- 站长推荐内容 -->
                    <div class="tab-pane active" id="recommend-content">
                        <ul class="clearfix bestlist-enhanced" role="list" aria-label="站长推荐网站列表">
                            {#foreach from=get_websites(0, 35, false, true) item=best#}
                            <li class="recommend-item">
                                <a href="/go.php?url=http://{#$best.web_url#}" title="{#$best.web_name#}" class="recommend-link" aria-label="访问推荐网站{#$best.web_name#}">
                                    <div class="recommend-icon">
                                        <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$best.web_url#}"
                                             width="28" height="28" alt="{#$best.web_name#}网站图标" loading="lazy" />
                                        <div class="recommend-badge" aria-label="推荐标识">推荐</div>
                                    </div>
                                    <span class="recommend-name">{#$best.web_name#}</span>
                                </a>
                            </li>
                            {#/foreach#}
                        </ul>
                    </div>

                    <!-- 待审核内容 -->
                    <div class="tab-pane" id="pending-content">
                        <ul class="clearfix bestlist-enhanced" role="list" aria-label="待审核网站列表">
                            {#foreach from=get_pending_websites(35) item=pending#}
                            <li class="recommend-item pending-item">
                                <a href="{#$pending.web_link#}" title="{#$pending.web_name#} - 待审核网站" class="recommend-link" aria-label="查看待审核网站{#$pending.web_name#}">
                                    <div class="recommend-icon">
                                        <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$pending.web_url#}"
                                             width="28" height="28" alt="{#$pending.web_name#}网站图标" loading="lazy" style="filter: hue-rotate(30deg) saturate(1.2);" />
                                        <div class="recommend-badge pending-badge" aria-label="待审核标识">待审</div>
                                    </div>
                                    <span class="recommend-name">{#$pending.web_name#}</span>
                                </a>
                            </li>
                            {#/foreach#}
                        </ul>
                    </div>

                    <!-- 黑名单内容 -->
                    <div class="tab-pane" id="blacklist-content">
                        <ul class="clearfix bestlist-enhanced" role="list" aria-label="黑名单网站列表">
                            {#foreach from=get_blacklist_websites(35) item=blacklist#}
                            <li class="recommend-item blacklist-item">
                                <a href="?mod=blacklist_detail&id={#$blacklist.web_id#}" title="{#$blacklist.web_name#} - 黑名单网站" class="recommend-link" aria-label="查看黑名单网站{#$blacklist.web_name#}">
                                    <div class="recommend-icon">
                                        <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$blacklist.web_url#}"
                                             width="28" height="28" alt="{#$blacklist.web_name#}网站图标" loading="lazy" style="filter: grayscale(100%) brightness(0.5);" />
                                        <div class="recommend-badge blacklist-badge" aria-label="黑名单标识">黑名单</div>
                                    </div>
                                    <span class="recommend-name">{#$blacklist.web_name#}</span>
                                </a>
                            </li>
                            {#/foreach#}
                        </ul>
                    </div>

                    <!-- 审核不通过内容 -->
                    <div class="tab-pane" id="rejected-content">
                        <ul class="clearfix bestlist-enhanced" role="list" aria-label="审核不通过网站列表">
                            {#foreach from=get_rejected_websites(35) item=rejected#}
                            <li class="recommend-item rejected-item">
                                <a href="?mod=rejected_detail&id={#$rejected.web_id#}" title="{#$rejected.web_name#} - 审核不通过网站{#if $rejected.web_reject_reason#} - 原因：{#$rejected.web_reject_reason#}{#/if#}" class="recommend-link" aria-label="查看审核不通过网站{#$rejected.web_name#}">
                                    <div class="recommend-icon">
                                        <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$rejected.web_url#}"
                                             width="28" height="28" alt="{#$rejected.web_name#}网站图标" loading="lazy" style="filter: hue-rotate(30deg) saturate(1.2);" />
                                        <div class="recommend-badge rejected-badge" aria-label="审核不通过标识">不通过</div>
                                    </div>
                                    <span class="recommend-name">{#$rejected.web_name#}</span>
                                </a>
                            </li>
                            {#/foreach#}
                        </ul>
                    </div>
                </div>
            </div>

            <div class="blank10"></div>

            <!-- 在线音乐播放器 -->
            <div id="musicbox" class="clearfix" aria-labelledby="music-player-title">
                <h3 id="music-player-title">
                    <span style="color: #e91e63; font-weight: bold; text-shadow: 0 1px 2px rgba(233, 30, 99, 0.3); font-size: 14px;">
                        <i class="fas fa-music" style="color: #e91e63; margin-right: 5px;" aria-hidden="true"></i>在线音乐
                    </span>
                </h3>
                <div id="music-player-container">
                    <!-- 音乐播放器控制面板 -->
                    <div class="music-controls">
                        <button id="prevBtn" class="control-btn" title="上一首">
                            <i class="fas fa-step-backward"></i>
                        </button>
                        <button id="playBtn" class="control-btn play-pause" title="播放/暂停">
                            <i class="fas fa-play"></i>
                        </button>
                        <button id="nextBtn" class="control-btn" title="下一首">
                            <i class="fas fa-step-forward"></i>
                        </button>
                        <button id="volumeBtn" class="control-btn" title="静音/取消静音">
                            <i class="fas fa-volume-up"></i>
                        </button>
                        <div class="volume-control">
                            <input type="range" id="volumeSlider" min="0" max="100" value="50" class="volume-slider" title="音量控制">
                        </div>
                    </div>

                    <!-- 当前播放信息 -->
                    <div class="music-info">
                        <div class="music-title" id="currentTitle">选择一首歌曲开始播放</div>
                        <div class="music-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                            <div class="time-info">
                                <span id="currentTime">0:00</span>
                                <span id="totalTime">0:00</span>
                            </div>
                        </div>
                    </div>

                    <!-- 音乐列表 -->
                    <div class="music-list">
                        <div class="list-header">
                            <span>播放列表</span>
                            <button id="refreshList" class="refresh-btn" title="刷新列表">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <ul id="musicList" class="music-items">
                            <!-- 音乐列表将通过JavaScript动态加载 -->
                            <li class="loading-item">正在加载音乐列表...</li>
                        </ul>
                    </div>

                    <!-- 隐藏的音频元素 -->
                    <audio id="audioPlayer" preload="none"></audio>
                </div>
            </div>

            <div class="blank10"></div>

            <!-- 酷站导航 -->
            <div id="coolbox" class="clearfix">
                <h3><i class="fas fa-star" style="color: #ffd700; margin-right: 5px;"></i>酷站导航</h3>
                <ul class="csitelist">
                    {#foreach from=get_best_categories() item=cate name=csite#}
                    <li>
                        <h4><a href="{#$cate.cate_link#}">{#$cate.cate_name#}</a></h4>
                        <a href="{#$cate.cate_link#}" class="more">更多>></a>
                        {#foreach from=get_websites($cate.cate_id, 8) item=cool#}
                        <span>
                            <a href="{#$cool.web_link#}" title="{#$cool.web_name#}">
                                <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$cool.web_url#}"
                                     width="18" height="18" />{#$cool.web_name#}
                            </a>
                        </span>
                        {#/foreach#}
                    </li>
                    {#if $smarty.foreach.csite.iteration % 5 == 0 && $smarty.foreach.csite.iteration != 20#}
                    <li class="sline"></li>
                    {#/if#}
                    {#/foreach#}
                </ul>
            </div>

            <div class="blank10"></div>

            <!-- 站点资讯 + 链接交换 -->
            <div id="rowbox" class="clearfix">
                <div id="newsbox">
                    <h3><i class="fas fa-newspaper" style="color: #17a2b8; margin-right: 5px;"></i>站点资讯</h3>
                    <span1><a href="?mod=article" class="more">更多>></a></span1>

                    <!-- 文章分类导航 -->
                    <div class="article-categories">
                        <a href="javascript:void(0);" class="article-cate-link active" data-cate-id="0">最新</a>
                        {#foreach from=get_categories() item=cate#}
                        {#if $cate.cate_mod == 'article' && $cate.cate_postcount > 0#}
                        <a href="javascript:void(0);" class="article-cate-link" data-cate-id="{#$cate.cate_id#}">{#$cate.cate_name#}</a>
                        {#/if#}
                        {#/foreach#}
                    </div>

                    <!-- 文章列表 -->
                    <div class="article-content-container">
                        <ul id="article-list" class="newslist">
                            {#foreach from=get_articles(0, 10, false) item=art#}
                            <li data-number="{#$idx+1#}">
                                <span>{#$art.art_ctime#}</span>
                                <a href="{#$art.art_link#}" title="{#$art.art_title#}">
                                    {#$art.art_title#}
                                    {#if $art.is_today#}<span class="new-icon">new</span>{#/if#}
                                </a>
                            </li>
                            {#/foreach#}
                        </ul>
                        <div id="article-error" style="display: none; text-align: center; padding: 10px; color: #d9534f;">
                            暂无文章
                        </div>
                    </div>
                </div>

                <div class="line"></div>

                <div id="exlink">
                    <h3><i class="fas fa-handshake" style="color: #fd7e14; margin-right: 5px;"></i>链接交换</h3>
                    <ul class="exlist">
                        {#foreach from=get_weblinks(0, 8) item=link#}
                        <li>
                            <a href="{#$link.web_link#}">
                                <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$link.web_url#}" width="18" height="18" />
                                {#$link.link_name#} - <img src="module/dr_badge.php?domain={#$link.web_url#}&size=small" alt="DR Badge" style="vertical-align: text-top; margin-top: -1px;" />，
                                百度权重{#$link.web_brank#}，{#$link.deal_type#}友情链接
                            </a>
                        </li>
                        {#/foreach#}
                    </ul>
                </div>
            </div>
        </div><!-- /homebox-right -->
    </div><!-- /homebox -->

    <div class="blank10"></div>

    <!-- ================= 最新点入 / 出 / 友情链接 ================= -->
    <div id="inbox" class="clearfix" aria-labelledby="latest-visits-title">
        <h3 id="latest-visits-title"><i class="fas fa-sign-in-alt" style="color: #28a745; margin-right: 5px;" aria-hidden="true"></i>最新点入</h3>
        <ul class="inlist" role="list" aria-label="最新点入网站列表">
            {#nocache#}
            {#foreach from=get_websites(0, 36, false, false, 'instat') item=instat#}
            <li>
                <a href="{#$instat.web_link#}" title="{#$instat.web_name#}" aria-label="访问{#$instat.web_name#}">
                    <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$instat.web_url#}"
                         width="16" height="16" alt="{#$instat.web_name#}图标" loading="lazy" />{#$instat.web_name#}
                </a>
            </li>
            {#/foreach#}
            {#/nocache#}
        </ul>
    </div>

    <div class="blank10"></div>

    <div id="inbox" class="clearfix" aria-labelledby="latest-outbound-title">
        <h3 id="latest-outbound-title"><i class="fas fa-sign-out-alt" style="color: #dc3545; margin-right: 5px;" aria-hidden="true"></i>最新点出</h3>
        <ul class="inlist" role="list" aria-label="最新点出网站列表">
            {#nocache#}
            {#foreach from=get_websites(0, 36, false, false, 'outstat') item=outstat#}
            <li>
                <a href="{#$outstat.web_link#}" title="{#$outstat.web_name#}" aria-label="访问{#$outstat.web_name#}">
                    <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://{#$outstat.web_url#}"
                         width="16" height="16" alt="{#$outstat.web_name#}图标" loading="lazy" />{#$outstat.web_name#}
                </a>
            </li>
            {#/foreach#}
            {#/nocache#}
        </ul>
    </div>

    <div class="blank10"></div>

    <div id="inbox" class="clearfix" aria-labelledby="friend-links-title">
        <h3 id="friend-links-title"><i class="fas fa-heart" style="color: #e91e63; margin-right: 5px;" aria-hidden="true"></i>友情链接</h3>
        <ul class="inlist" role="list" aria-label="友情链接列表">
            {#foreach from=get_links() item=link#}
            <li>
                <a href="{#$link.link_url#}" target="_blank" rel="noopener noreferrer" title="{#$link.link_name#}" aria-label="访问友情链接{#$link.link_name#}">
                    <img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url={#$link.link_url#}"
                         width="16" height="16" alt="{#$link.link_name#}图标" loading="lazy" />{#$link.link_name#}
                </a>
            </li>
            {#/foreach#}
        </ul>
    </div>


    <div class="blank10"></div>

    <!-- 今日访问统计 -->
    <div id="todayStatsBox" class="clearfix">
        <h3>📊 今日访问统计</h3>
        <div class="stats-grid">
            <div class="stat-card" data-tooltip="今日网站总访问量">
                <div class="stat-icon">👁️</div>
                <div class="stat-label">总浏览</div>
                <div class="stat-value" id="todayTotalVisits">加载中...</div>
            </div>
            <div class="stat-card" data-tooltip="今日站点页面访问量">
                <div class="stat-icon">🏠</div>
                <div class="stat-label">站点浏览</div>
                <div class="stat-value" id="todaySiteVisits">加载中...</div>
            </div>
            <div class="stat-card" data-tooltip="今日文章页面访问量">
                <div class="stat-icon">📄</div>
                <div class="stat-label">文章浏览</div>
                <div class="stat-value" id="todayArticleVisits">加载中...</div>
            </div>
            <div class="stat-card" data-tooltip="今日用户点击外链次数">
                <div class="stat-icon">🔗</div>
                <div class="stat-label">出站次数</div>
                <div class="stat-value" id="todayOutlinks">加载中...</div>
            </div>
        </div>
    </div>

    <!-- 当日蜘蛛统计 -->
    <div class="blank10"></div>
    <div id="spiderStatsBox" class="clearfix">
        <h3>🕷️ 当日蜘蛛统计</h3>
        <div class="spider-grid">
            <div class="spider-card" data-tooltip="Google搜索引擎爬虫访问次数">
                <div class="spider-icon" style="color: #4285f4;">G</div>
                <div class="spider-label">Google</div>
                <div class="spider-value" id="spiderGoogle">0</div>
            </div>
            <div class="spider-card" data-tooltip="百度搜索引擎爬虫访问次数">
                <div class="spider-icon" style="color: #2932e1;">百</div>
                <div class="spider-label">Baidu</div>
                <div class="spider-value" id="spiderBaidu">0</div>
            </div>
            <div class="spider-card" data-tooltip="Bing搜索引擎爬虫访问次数">
                <div class="spider-icon" style="color: #008373;">B</div>
                <div class="spider-label">Bing</div>
                <div class="spider-value" id="spiderBing">0</div>
            </div>
            <div class="spider-card" data-tooltip="搜狗搜索引擎爬虫访问次数">
                <div class="spider-icon" style="color: #fb6c00;">搜</div>
                <div class="spider-label">Sogou</div>
                <div class="spider-value" id="spiderSogou">0</div>
            </div>
            <div class="spider-card" data-tooltip="360搜索引擎爬虫访问次数">
                <div class="spider-icon" style="color: #01c101;">360</div>
                <div class="spider-label">360</div>
                <div class="spider-value" id="spiderSo360">0</div>
            </div>
            <div class="spider-card" data-tooltip="字节跳动搜索爬虫访问次数">
                <div class="spider-icon" style="color: #ff3040;">字</div>
                <div class="spider-label">Byte</div>
                <div class="spider-value" id="spiderBytedance">0</div>
            </div>
        </div>
    </div>

    <div class="blank10"></div>

    {#include file="footer.html"#}
</div><!-- /wrapper -->

<!-- SEO优化 - 结构化数据：网站导航 -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "SiteNavigationElement",
    "name": "网站导航",
    "url": "{#$site_url#}",
    "hasPart": [
        {
            "@type": "SiteNavigationElement",
            "name": "网站目录",
            "url": "{#$site_url#}?mod=webdir"
        },
        {
            "@type": "SiteNavigationElement",
            "name": "VIP站点",
            "url": "{#$site_url#}?mod=vip_list"
        },
        {
            "@type": "SiteNavigationElement",
            "name": "站长资讯",
            "url": "{#$site_url#}?mod=article"
        },
        {
            "@type": "SiteNavigationElement",
            "name": "链接交换",
            "url": "{#$site_url#}?mod=weblink"
        },
        {
            "@type": "SiteNavigationElement",
            "name": "最新收录",
            "url": "{#$site_url#}?mod=update"
        }
    ]
}
</script>

<!-- SEO优化 - 结构化数据：网站统计信息 -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Dataset",
    "name": "95目录网站统计",
    "description": "95目录网收录的网站统计数据",
    "url": "{#$site_url#}",
    "keywords": ["网站目录", "网站收录", "网站分类", "站长工具"],
    "creator": {
        "@type": "Organization",
        "name": "95目录网"
    }
}
</script>

<script>
// 统一弹窗功能
function showDonatePopup() {
    document.getElementById('donate-popup').style.display = 'block';
}

function closeDonatePopup() {
    document.getElementById('donate-popup').style.display = 'none';
}

// 点击弹窗外部关闭
document.addEventListener('DOMContentLoaded', function() {
    const donatePopup = document.getElementById('donate-popup');
    if (donatePopup) {
        donatePopup.addEventListener('click', function(e) {
            if (e.target === this) {
                closeDonatePopup();
            }
        });
    }

    // 初始化站点资讯分类切换功能
    initArticleSwitch();

    // 初始化省份标签搜索功能
    initProvinceTagsSearch();
});

// ESC键关闭弹窗
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeDonatePopup();
    }
});

// 站点资讯分类切换功能
function initArticleSwitch() {
    let autoSwitchTimer = null;
    let currentCateIndex = 0;
    let isHovering = false;

    // 获取所有分类链接
    const cateLinks = document.querySelectorAll('.article-cate-link');

    console.log('文章分类链接数量:', cateLinks.length); // 调试信息

    if (cateLinks.length === 0) return;

    // 默认加载最新文章
    loadArticleData(0);

    // 绑定分类链接点击事件
    cateLinks.forEach(function(link, index) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const cateId = this.getAttribute('data-cate-id');

            // 更新活动状态
            cateLinks.forEach(function(l) {
                l.classList.remove('active');
            });
            this.classList.add('active');

            // 更新当前索引
            currentCateIndex = index;

            // 加载对应分类的文章
            loadArticleData(cateId);

            // 重置自动切换
            resetAutoSwitch();
        });

        // 鼠标悬停时停止自动切换
        link.addEventListener('mouseenter', function() {
            isHovering = true;
            clearTimeout(autoSwitchTimer);
        });

        // 鼠标离开时恢复自动切换
        link.addEventListener('mouseleave', function() {
            isHovering = false;
            startAutoSwitch();
        });
    });

    // 启动自动切换
    startAutoSwitch();

    function startAutoSwitch() {
        if (isHovering) return;

        autoSwitchTimer = setTimeout(function() {
            if (!isHovering && cateLinks.length > 1) {
                // 添加滚动效果
                const currentLink = cateLinks[currentCateIndex];
                console.log('添加滚动效果到:', currentLink.textContent); // 调试信息
                currentLink.classList.add('scrolling');

                // 切换到下一个分类
                currentCateIndex = (currentCateIndex + 1) % cateLinks.length;
                const nextLink = cateLinks[currentCateIndex];

                // 延迟一点时间显示滚动效果
                setTimeout(function() {
                    currentLink.classList.remove('scrolling');
                    console.log('点击下一个链接:', nextLink.textContent); // 调试信息
                    nextLink.click();
                }, 300);
            }
        }, 5000); // 5秒自动切换
    }

    function resetAutoSwitch() {
        clearTimeout(autoSwitchTimer);
        startAutoSwitch();
    }
}

function loadArticleData(cateId) {
    const listEl = document.getElementById('article-list');
    const errorEl = document.getElementById('article-error');

    if (!listEl || !errorEl) return;

    // 直接隐藏错误状态，显示列表
    errorEl.style.display = 'none';
    listEl.style.display = 'block';

    // 构造请求URL
    const url = '?mod=article&cate_id=' + cateId + '&ajax=1&limit=10';

    // 发送AJAX请求
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.articles) {
                displayArticles(data.articles);
            } else {
                showArticleError('暂无文章');
            }
        })
        .catch(error => {
            console.error('加载失败:', error);
            showArticleError('加载失败，请稍后重试');
        });
}

function displayArticles(articles) {
    const listEl = document.getElementById('article-list');

    if (articles.length === 0) {
        showArticleError('该分类暂无文章');
        return;
    }

    let html = '';
    articles.forEach(function(article, index) {
        html += '<li data-number="' + (index + 1) + '">';
        html += '<span>' + article.art_ctime + '</span>';
        html += '<a href="' + article.art_link + '" title="' + article.art_title + '">';
        html += article.art_title;
        // 如果是当天发表的文章，添加new图标
        if (article.is_today) {
            html += '<span class="new-icon">new</span>';
        }
        html += '</a>';
        html += '</li>';
    });

    listEl.innerHTML = html;
    listEl.style.display = 'block';
}

function showArticleError(message) {
    const listEl = document.getElementById('article-list');
    const errorEl = document.getElementById('article-error');

    listEl.innerHTML = '';
    listEl.style.display = 'none';
    errorEl.textContent = message;
    errorEl.style.display = 'block';
}

// 省份标签搜索功能
function initProvinceTagsSearch() {
    const provinceTags = document.querySelectorAll('.province-tag');

    provinceTags.forEach(function(tag) {
        // 鼠标点击事件
        tag.addEventListener('click', function() {
            handleProvinceSearch(this);
        });

        // 键盘事件支持（Enter和Space键）
        tag.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleProvinceSearch(this);
            }
        });
    });
}

function handleProvinceSearch(tagElement) {
    const province = tagElement.getAttribute('data-province');

    if (!province) return;

    // 添加点击动画效果
    tagElement.classList.add('clicked');
    setTimeout(function() {
        tagElement.classList.remove('clicked');
    }, 300);

    // 执行搜索
    performProvinceSearch(province);
}

function performProvinceSearch(province) {
    // 构建搜索URL
    const searchUrl = '?mod=search&type=name&query=' + encodeURIComponent(province);

    // 跳转到搜索结果页面
    window.location.href = searchUrl;
}

// 为省份标签添加搜索提示功能
function addProvinceSearchTooltips() {
    const provinceTags = document.querySelectorAll('.province-tag');

    provinceTags.forEach(function(tag) {
        const province = tag.getAttribute('data-province');
        tag.title = '点击搜索' + province + '地区的网站';
    });
}

// 页面加载完成后添加提示
document.addEventListener('DOMContentLoaded', function() {
    addProvinceSearchTooltips();
});
</script>



</body>
</html>