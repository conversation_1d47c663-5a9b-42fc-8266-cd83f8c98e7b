    	</div>
    </div>
    	<div id="footer" class="clearfix">
    	    {#$site_copyright#}
    	    <p>{#insert name="script_time"#}</p>
<!-- 在需要显示的位置添加 -->
<div id="onlineCounter">当前在线：加载中...</div>

<script>
// 定时更新（每60秒）
function updateCounter() {
    fetch('/data/online_stats/online.php')
        .then(response => response.text())
        .then(count => {
            document.getElementById('onlineCounter').innerHTML = 
                `当前在线：${count}人`;
        })
        .catch(() => console.log('统计服务不可用'));
}

// 页面加载时立即执行
updateCounter();
// 设置定时器
setInterval(updateCounter, 60000);
</script>

    </div>
</body>
</html>