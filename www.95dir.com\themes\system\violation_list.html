<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>违规网站管理</title>
<link href="../themes/system/skin/global.css" rel="stylesheet" type="text/css" />
<link href="../themes/system/skin/iframe.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../public/scripts/jquery.min.js"></script>
</head>

<body>
<div id="wrapper">
    <div id="header">
        <h1>违规网站管理</h1>
        <div class="nav-tabs">
            <a href="violation_simple_working.php?action=list" class="active">违规列表</a>
            <a href="violation_simple_working.php?action=config">配置管理</a>
            <a href="violation_simple_working.php?action=logs">检查日志</a>
            <a href="violation_simple_working.php?action=check">手动检查</a>
        </div>
    </div>
    
    <div id="content">
        <!-- 统计信息 -->
        <div class="stats-box">
            <div class="stat-item">
                <span class="stat-number">{#$stats.total_violations#}</span>
                <span class="stat-label">总违规网站</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{#$stats.today_violations#}</span>
                <span class="stat-label">今日新增</span>
            </div>
        </div>
        
        <!-- 消息提示 -->
        {#if isset($smarty.get.message)#}
        <div class="message {#$smarty.get.type#}">
            {#$smarty.get.message#}
        </div>
        {#/if#}
        
        <!-- 违规网站列表 -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th width="60">ID</th>
                        <th width="200">网站名称</th>
                        <th width="200">网站URL</th>
                        <th width="120">分类</th>
                        <th width="250">违规原因</th>
                        <th width="120">违规时间</th>
                        <th width="100">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {#foreach from=$violations item=violation#}
                    <tr>
                        <td>{#$violation.web_id#}</td>
                        <td>
                            <strong>{#$violation.web_name#}</strong>
                            {#if $violation.web_intro#}
                            <br><small style="color: #666;">{#$violation.web_intro|truncate:50#}</small>
                            {#/if#}
                        </td>
                        <td>
                            <span style="color: #999; font-size: 12px;">
                                {#$violation.web_url|truncate:30#}
                            </span>
                        </td>
                        <td>{#$violation.cate_name#}</td>
                        <td>
                            <span style="color: #d9534f;">
                                {#$violation.web_violation_reason#}
                            </span>
                        </td>
                        <td>
                            {#$violation.web_violation_time|date_format:"%m-%d %H:%M"#}
                        </td>
                        <td>
                            <a href="violation_simple_working.php?action=restore&web_id={#$violation.web_id#}" 
                               onclick="return confirm('确认恢复此网站吗？')" 
                               class="btn btn-sm btn-success">恢复</a>
                        </td>
                    </tr>
                    {#foreachelse#}
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 50px; color: #999;">
                            暂无违规网站
                        </td>
                    </tr>
                    {#/foreach#}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {#if $pages > 1#}
        <div class="pagination">
            {#if $page > 1#}
            <a href="violation_simple_working.php?action=list&page={#$page-1#}" class="btn">上一页</a>
            {#/if#}
            
            {#for $i=1 to $pages#}
                {#if $i == $page#}
                <span class="current">{#$i#}</span>
                {#else#}
                <a href="violation_simple_working.php?action=list&page={#$i#}">{#$i#}</a>
                {#/if#}
            {#/for#}
            
            {#if $page < $pages#}
            <a href="violation_simple_working.php?action=list&page={#$page+1#}" class="btn">下一页</a>
            {#/if#}
        </div>
        {#/if#}
        
        <div class="info-box">
            <h3>说明：</h3>
            <ul>
                <li>违规网站已自动隐藏，前台不会显示URL和跳转链接</li>
                <li>如果发现误判，可以点击"恢复"按钮恢复网站正常状态</li>
                <li>系统会定期自动检查网站内容，发现违规自动标记</li>
                <li>可以在"配置管理"中调整违规关键词和检查参数</li>
            </ul>
        </div>
    </div>
</div>

<style>
.nav-tabs {
    margin: 10px 0;
    border-bottom: 1px solid #ddd;
}

.nav-tabs a {
    display: inline-block;
    padding: 8px 16px;
    margin-right: 5px;
    text-decoration: none;
    color: #666;
    border: 1px solid #ddd;
    border-bottom: none;
    background: #f5f5f5;
}

.nav-tabs a.active {
    background: #fff;
    color: #333;
    font-weight: bold;
}

.stats-box {
    display: flex;
    margin: 20px 0;
    gap: 20px;
}

.stat-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
    text-align: center;
    min-width: 120px;
}

.stat-number {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #d9534f;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.message {
    padding: 10px;
    margin: 10px 0;
    border-radius: 3px;
}

.message.success {
    background: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
}

.message.error {
    background: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
}

.table-container {
    margin: 20px 0;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: #f5f5f5;
    padding: 10px;
    border-bottom: 1px solid #ddd;
    text-align: left;
    font-weight: bold;
}

.data-table td {
    padding: 10px;
    border-bottom: 1px solid #eee;
    vertical-align: top;
}

.data-table tr:hover {
    background: #f9f9f9;
}

.btn {
    display: inline-block;
    padding: 4px 8px;
    text-decoration: none;
    border-radius: 3px;
    font-size: 12px;
}

.btn-success {
    background: #5cb85c;
    color: white;
}

.btn-success:hover {
    background: #449d44;
}

.pagination {
    text-align: center;
    margin: 20px 0;
}

.pagination a, .pagination span {
    display: inline-block;
    padding: 5px 10px;
    margin: 0 2px;
    text-decoration: none;
    border: 1px solid #ddd;
}

.pagination .current {
    background: #337ab7;
    color: white;
    border-color: #337ab7;
}

.info-box {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
}

.info-box h3 {
    margin-top: 0;
    color: #333;
}

.info-box ul {
    margin: 10px 0;
    padding-left: 20px;
}

.info-box li {
    margin: 5px 0;
    color: #666;
}
</style>

</body>
</html>
