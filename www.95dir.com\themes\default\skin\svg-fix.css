/**
 * SVG图片显示修复CSS
 * 解决网站详情页SVG图片不显示的问题
 */

/* ===========================================
   SVG图片通用修复
   =========================================== */

/* 确保SVG图片正确显示 */
img[src$=".svg"] {
    display: block !important;
    max-width: 100% !important;
    height: auto !important;
    object-fit: contain !important;
}

/* 网站详情页缩略图SVG修复 */
.wthumb img[src$=".svg"],
img.wthumb[src$=".svg"] {
    width: 130px !important;
    height: 110px !important;
    object-fit: contain !important;
    background: #fff !important;
    border: 1px solid #dadada !important;
    padding: 3px !important;
    display: block !important;
}

/* 相关网站列表SVG修复 */
.rellist li img[src$=".svg"] {
    width: 100px !important;
    height: 80px !important;
    object-fit: contain !important;
    background: #fff !important;
    padding: 2px !important;
    border: 1px solid #e8e8e8 !important;
}

/* 网站列表页SVG修复 */
.sitelist li .thumb img[src$=".svg"] {
    width: 100px !important;
    height: 80px !important;
    object-fit: contain !important;
    background: #fff !important;
    border: 1px solid #d7dde3 !important;
    padding: 3px !important;
}

/* 推荐站点SVG修复 */
.weblist_b li img[src$=".svg"] {
    width: 85px !important;
    height: 65px !important;
    object-fit: contain !important;
    background: #fff !important;
    border: 1px solid #dbdbdb !important;
}

/* 最佳站点SVG修复 */
.bestlist li img[src$=".svg"] {
    width: auto !important;
    height: auto !important;
    max-width: 100px !important;
    max-height: 80px !important;
    object-fit: contain !important;
    background: #fff !important;
    padding: 3px !important;
    border: 1px solid #e8e8e8 !important;
}

/* IDC列表SVG修复 */
.idclist li img[src$=".svg"] {
    width: auto !important;
    height: auto !important;
    max-width: 100px !important;
    max-height: 60px !important;
    object-fit: contain !important;
    background: #fff !important;
    padding: 3px !important;
    border: 1px solid #e8e8e8 !important;
}

/* ===========================================
   SVG浏览器兼容性修复
   =========================================== */

/* IE浏览器SVG支持 */
.ie img[src$=".svg"] {
    width: 130px;
    height: 110px;
}

/* 移动端SVG优化 */
@media (max-width: 767px) {
    .wthumb img[src$=".svg"],
    img.wthumb[src$=".svg"] {
        width: 100% !important;
        height: auto !important;
        max-height: 110px !important;
    }
    
    .sitelist li .thumb img[src$=".svg"] {
        width: 100% !important;
        height: auto !important;
        max-height: 75px !important;
    }
}

/* ===========================================
   SVG加载状态指示
   =========================================== */

/* SVG加载中状态 */
img[src$=".svg"]:not([data-loaded]) {
    background: #f5f5f5 url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxjaXJjbGUgY3g9IjIwIiBjeT0iMjAiIHI9IjE4IiBmaWxsPSJub25lIiBzdHJva2U9IiNjY2MiIHN0cm9rZS13aWR0aD0iMiIvPgogICAgPHRleHQgeD0iMjAiIHk9IjI1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXNpemU9IjEwIiBmaWxsPSIjOTk5Ij5TVkc8L3RleHQ+Cjwvc3ZnPg==') center no-repeat !important;
    background-size: 40px 40px !important;
}

/* SVG加载失败状态 */
img[src$=".svg"][data-error="true"] {
    background: #ffe6e6 url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxyZWN0IHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgZmlsbD0iI2ZmZTZlNiIgc3Ryb2tlPSIjZmY2NjY2IiBzdHJva2Utd2lkdGg9IjIiLz4KICAgIDx0ZXh0IHg9IjIwIiB5PSIyNSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSI4IiBmaWxsPSIjZmY2NjY2Ij7plJnor688L3RleHQ+Cjwvc3ZnPg==') center no-repeat !important;
    background-size: 40px 40px !important;
    border: 2px solid #ff6666 !important;
}

/* ===========================================
   SVG动画效果
   =========================================== */

/* SVG悬停效果 */
img[src$=".svg"]:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 禁用某些元素的悬停效果 */
.rellist li img[src$=".svg"]:hover,
.sitelist li .thumb img[src$=".svg"]:hover {
    transform: none;
    box-shadow: none;
}

/* ===========================================
   SVG降级处理
   =========================================== */

/* 为不支持SVG的浏览器提供降级方案 */
.no-svg img[src$=".svg"] {
    display: none !important;
}

.no-svg img[src$=".svg"]::after {
    content: "图片";
    display: block;
    width: 130px;
    height: 110px;
    background: #f5f5f5;
    border: 1px solid #ddd;
    text-align: center;
    line-height: 110px;
    color: #999;
    font-size: 12px;
}

/* ===========================================
   调试模式
   =========================================== */

/* 调试模式：显示SVG边框 */
.debug-svg img[src$=".svg"] {
    border: 2px solid #00ff00 !important;
    box-shadow: 0 0 5px rgba(0,255,0,0.5) !important;
}

.debug-svg img[src$=".svg"]::before {
    content: "SVG";
    position: absolute;
    top: -20px;
    left: 0;
    background: #00ff00;
    color: white;
    padding: 2px 5px;
    font-size: 10px;
    z-index: 1000;
}

/* ===========================================
   打印样式
   =========================================== */

@media print {
    img[src$=".svg"] {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
}

/* ===========================================
   高DPI屏幕优化
   =========================================== */

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    img[src$=".svg"] {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* ===========================================
   无障碍访问
   =========================================== */

/* 确保SVG图片有正确的alt属性处理 */
img[src$=".svg"][alt=""] {
    alt: "SVG图片";
}

/* 为屏幕阅读器提供更好的支持 */
img[src$=".svg"]:not([alt]) {
    role: "img";
    aria-label: "网站图标";
}

/* ===========================================
   性能优化
   =========================================== */

/* 预加载关键SVG */
.preload-svg {
    position: absolute;
    left: -9999px;
    top: -9999px;
    width: 1px;
    height: 1px;
    opacity: 0;
}

/* 延迟加载SVG */
img[src$=".svg"][loading="lazy"] {
    transition: opacity 0.3s ease;
}

img[src$=".svg"][loading="lazy"]:not([data-loaded]) {
    opacity: 0.5;
}

img[src$=".svg"][loading="lazy"][data-loaded] {
    opacity: 1;
}
