<!DOCTYPE html>
<html lang="zh" dir="auto">

<head><meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="robots" content="index, follow">
<title>404 Page not found | 3rd&#39;s Blog-求是</title>
<meta name="keywords" content="">
<meta name="description" content="">
<meta name="author" content="3rd">
<link rel="canonical" href="https://333rd.net/zh/404.html">
<link crossorigin="anonymous" href="/assets/css/stylesheet.a1e0be936078f9f7d6c084eaa2156083fed994068099ab733342d34549b93031.css" integrity="sha256-oeC&#43;k2B4&#43;ffWwITqohVgg/7ZlAaAmatzM0LTRUm5MDE=" rel="preload stylesheet" as="style">
<link rel="icon" href="https://333rd.net/img/logo.png">
<link rel="icon" type="image/png" sizes="16x16" href="https://333rd.net/favicon-16x16.png">
<link rel="icon" type="image/png" sizes="32x32" href="https://333rd.net/favicon-32x32.png">
<link rel="apple-touch-icon" href="https://333rd.net/apple-touch-icon.png">
<link rel="mask-icon" href="https://333rd.net/safari-pinned-tab.svg">
<meta name="theme-color" content="#2e2e33">
<meta name="msapplication-TileColor" content="#2e2e33">
<link rel="alternate" hreflang="zh" href="https://333rd.net/zh/404.html">
<noscript>
    <style>
        #theme-toggle,
        .top-link {
            display: none;
        }

    </style>
    <style>
        @media (prefers-color-scheme: dark) {
            :root {
                --theme: rgb(29, 30, 32);
                --entry: rgb(46, 46, 51);
                --primary: rgb(218, 218, 219);
                --secondary: rgb(155, 156, 157);
                --tertiary: rgb(65, 66, 68);
                --content: rgb(196, 196, 197);
                --code-block-bg: rgb(46, 46, 51);
                --code-bg: rgb(55, 56, 62);
                --border: rgb(51, 51, 51);
            }

            .list {
                background: var(--theme);
            }

            .list:not(.dark)::-webkit-scrollbar-track {
                background: 0 0;
            }

            .list:not(.dark)::-webkit-scrollbar-thumb {
                border-color: var(--theme);
            }
        }

    </style>
</noscript>



<script async src="//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js"></script>
  <meta name="referrer" content="no-referrer-when-downgrade"><meta property="og:url" content="https://333rd.net/zh/404.html">
  <meta property="og:site_name" content="3rd&#39;s Blog-求是">
  <meta property="og:title" content="404 Page not found">
  <meta property="og:locale" content="zh">
  <meta property="og:type" content="website">
<meta name="twitter:card" content="summary">
<meta name="twitter:title" content="404 Page not found">
<meta name="twitter:description" content="">

</head>

<body class="list" id="top">
<script>
    if (localStorage.getItem("pref-theme") === "dark") {
        document.body.classList.add('dark');
    } else if (localStorage.getItem("pref-theme") === "light") {
        document.body.classList.remove('dark')
    } else if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
        document.body.classList.add('dark');
    }

</script>

<header class="header">
    <nav class="nav">
        <div class="logo">
            <a href="https://333rd.net/zh/" accesskey="h" title="3rd&#39;s Blog (Alt + H)">
                <img src="https://333rd.net/img/logo.png" alt="" aria-label="logo"
                    height="35">3rd&#39;s Blog</a>
            <div class="logo-switches">
                <button id="theme-toggle" accesskey="t" title="(Alt + T)" aria-label="Toggle theme">
                    <svg id="moon" xmlns="http://www.w3.org/2000/svg" width="24" height="18" viewBox="0 0 24 24"
                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                    </svg>
                    <svg id="sun" xmlns="http://www.w3.org/2000/svg" width="24" height="18" viewBox="0 0 24 24"
                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                </button>
            </div>
        </div>
        <ul id="menu">
            <li>
                <a href="https://333rd.net/zh/search" title="🔍搜索 (Alt &#43; /)" accesskey=/>
                    <span>🔍搜索</span>
                </a>
            </li>
            <li>
                <a href="https://333rd.net/zh/" title="🏠主页">
                    <span>🏠主页</span>
                </a>
            </li>
            <li>
                <a href="https://333rd.net/zh/posts" title="📚文章">
                    <span>📚文章</span>
                </a>
            </li>
            <li>
                <a href="https://333rd.net/zh/archives" title="⏱时间轴">
                    <span>⏱时间轴</span>
                </a>
            </li>
            <li>
                <a href="https://333rd.net/zh/tags" title="🔖标签">
                    <span>🔖标签</span>
                </a>
            </li>
            <li>
                <a href="https://333rd.net/zh/about" title="💬关于">
                    <span>💬关于</span>
                </a>
            </li>
            <li>
                <a href="https://333rd.net/zh/links" title="🤝友链">
                    <span>🤝友链</span>
                </a>
            </li>
        </ul>
    </nav>
</header>
<main class="main">
<div class="not-found" style="text-align: center;">
    <img src="/img/NotFound.png" alt="404Notfound">
</div>
    </main>
    
<footer class="footer">
        <span>&copy; 2025 <a href="https://333rd.net/zh/">3rd&#39;s Blog-求是</a></span> · 

    <span>
        Powered by
        <a href="https://gohugo.io/" rel="noopener noreferrer" target="_blank">Hugo</a> &
        <a href="https://github.com/adityatelange/hugo-PaperMod/" rel="noopener" target="_blank">PaperMod</a>
    </span>
</footer>
<a href="#top" aria-label="go to top" title="Go to Top (Alt + G)" class="top-link" id="top-link" accesskey="g">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 6" fill="currentColor">
        <path d="M12 6H0l6-6z" />
    </svg>
</a>
<div class="busuanzi-footer" style="font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; text-align: center; padding: 4px 0; color: #999;">
  <span id="busuanzi_container_site_pv" style="margin-right: 8px; font-size: 0.85em;">
    本站总访问量<span id="busuanzi_value_site_pv" style="font-weight: 500;">0</span>次
  </span>
  <span id="busuanzi_container_site_uv" style="font-size: 0.85em;">
    本站访客数<span id="busuanzi_value_site_uv" style="font-weight: 500;">0</span>人次
  </span>
</div>

<script>
    let menu = document.getElementById('menu')
    if (menu) {
        menu.scrollLeft = localStorage.getItem("menu-scroll-position");
        menu.onscroll = function () {
            localStorage.setItem("menu-scroll-position", menu.scrollLeft);
        }
    }

    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener("click", function (e) {
            e.preventDefault();
            var id = this.getAttribute("href").substr(1);
            if (!window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                document.querySelector(`[id='${decodeURIComponent(id)}']`).scrollIntoView({
                    behavior: "smooth"
                });
            } else {
                document.querySelector(`[id='${decodeURIComponent(id)}']`).scrollIntoView();
            }
            if (id === "top") {
                history.replaceState(null, null, " ");
            } else {
                history.pushState(null, null, `#${id}`);
            }
        });
    });

</script>
<script>
    var mybutton = document.getElementById("top-link");
    window.onscroll = function () {
        if (document.body.scrollTop > 800 || document.documentElement.scrollTop > 800) {
            mybutton.style.visibility = "visible";
            mybutton.style.opacity = "1";
        } else {
            mybutton.style.visibility = "hidden";
            mybutton.style.opacity = "0";
        }
    };

</script>
<script>
    document.getElementById("theme-toggle").addEventListener("click", () => {
        if (document.body.className.includes("dark")) {
            document.body.classList.remove('dark');
            localStorage.setItem("pref-theme", 'light');
        } else {
            document.body.classList.add('dark');
            localStorage.setItem("pref-theme", 'dark');
        }
    })

</script>
</body>

</html>
