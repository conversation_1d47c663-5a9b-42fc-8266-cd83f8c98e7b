/* LOGO预览优化样式 - 仅渐变背景效果 */

/* 渐变背景容器 - 保持原有布局，只添加渐变背景 */
.logo-bg-container {
    background: linear-gradient(45deg, #ffffff, #ffa994, #fd3c3c, #ff9e88, #ffffff, #ffddea, #ff96df);
    background-size: 400% 400%;
    animation: gradientAnimation 10s ease infinite;
    border-radius: 10px;
}

/* 渐变动画 */
@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 加载失败样式 */
.logo-preview-error {
    padding: 20px;
    text-align: center;
    color: #999;
    font-size: 12px;
    background: linear-gradient(45deg, #ffffff, #ffa994, #fd3c3c, #ff9e88, #ffffff, #ffddea, #ff96df);
    background-size: 400% 400%;
    animation: gradientAnimation 10s ease infinite;
    border-radius: 10px;
}

/* 打印样式 */
@media print {
    .logo-bg-container {
        background: white !important;
        animation: none;
    }
}
