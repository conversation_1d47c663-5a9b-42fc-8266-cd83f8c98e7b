//搜索
$(document).ready(function(){
    $("#selopt").hover(
        function(){
            $("#options").slideDown();
            $("#options li a").click(function(){
                $("#cursel").text($(this).text());
                $("#type").attr("value", $(this).attr("name"));
                $("#options").hide();
            });
        },
        
        function(){$("#options").hide();}
    )   
})

//搜索伪静态
function rewrite_search(){
	var type = $("#type").val();
	var query = $.trim($("#query").val());
	if (type == null) {type = "tags"}
	if (query == "") {
		alert("\u8bf7\u8f93\u5165\u641c\u7d22\u5173\u952e\u5b57\uff01");
		$("#query").focus();
		return false;
	} else {
		if (rewrite == 1) {
			window.location.href = sitepath + "search-" + type + "-" + encodeURI(query) + ".html";
		} else if (rewrite == 2) {
			window.location.href = sitepath + "search/" + type + "/" + encodeURI(query) + ".html";
		} else if (rewrite == 3) {
			window.location.href = sitepath + "search/" + type + "/" + encodeURI(query);
		} else {
			this.form.submit();
		}
	}
	return false;
}

//验证url
function checkurl(url){
	if (url == '') {
		$("#msg").html('请输入网站域名！');
		return false;
	}
	
	$(document).ready(function(){$("#msg").html('<img src="' + sitepath + 'public/images/loading.gif" align="absmiddle"> 正在验证，请稍候...'); $.ajax({type: "GET", url: sitepath + '?mod=ajaxget&type=check', data: 'url=' + url, cache: false, success: function(data){$("#msg").html(data)}});});
return true;
};

//获取META
function getmeta() {
	var url = $("#web_url").attr("value");
	if (url == '') {
		alert('请输入网站域名！');
		$("#web_url").focus();
		return false;
	}
	$(document).ready(function(){$("#meta_btn").val('正在获取，请稍候...'); $.ajax({type: "GET", url: sitepath + '?mod=ajaxget&type=crawl', data: 'url=' + url, datatype: "script", cache: false, success: function(data){$("body").append(data); $("#meta_btn").val('重新获取');}});});
}

//获取IP, PageRank, Sogou PageRank, Alexa
function getdata() {
	var url = $("#web_url").attr("value");
	if (url == '') {
		alert('请输入网站域名！');
		$("#web_url").focus();
		return false;
	}

	// 显示加载状态
	$("#data_btn").val('正在获取，请稍候...').prop('disabled', true);

	$.ajax({
		type: "GET",
		url: sitepath + '?mod=ajaxget&type=data',
		data: 'url=' + encodeURIComponent(url),
		dataType: "html",
		cache: false,
		timeout: 120000, // 2分钟超时
		success: function(data) {
			console.log('获取数据成功:', data);
			$("body").append(data);
			// 注意：按钮状态由服务器端的JavaScript代码控制
		},
		error: function(xhr, status, error) {
			console.error('获取数据失败:', status, error);
			alert('获取数据失败，请稍后重试。错误信息：' + error);
			$("#data_btn").val('重新获取').prop('disabled', false);
		}
	});
}

//添加收藏
function addfav(wid) {
	$(document).ready(function(){$.ajax({type: "GET", url: sitepath + "?mod=getdata&type=addfav", data: "wid=" + wid, cache: false, success: function(data){$("body").append(data)}});});
};

//点出统计
function clickout(wid) {
	$(document).ready(function(){$.ajax({type: "GET", url: sitepath + "?mod=getdata&type=outstat", data: "wid=" + wid, cache: false, success: function(data){}});});
};

//错误报告
function report(obj, wid) {
	$(document).ready(function(){if (confirm("确认报告此错误吗？")){ $("#" + obj).html("正在提交，请稍候..."); $.ajax({type: "GET", url: sitepath + "?mod=getdata&type=error", data: "wid=" + wid, cache: false, success: function(data){$("#" + obj).html(data);}})};});
};

//验证码
function refreshimg(obj) {
	var randnum = Math.random();
	$("#" + obj).html('<img src="' + sitepath + 'source/include/captcha.php?s=' + randnum + '" align="absmiddle" alt="看不清楚?换一张" onclick="this.src+='+ randnum +'" style="cursor: pointer;">');
}

    // 显示打赏弹窗
    function showDonatePopup() {
        document.getElementById('donate-popup').style.display = 'flex';
    }

    // 关闭打赏弹窗
    function closeDonatePopup() {
        document.getElementById('donate-popup').style.display = 'none';
    }

    // 点击弹窗外部关闭
    window.onclick = function(event) {
        var popup = document.getElementById('donate-popup');
        if (event.target == popup) {
            popup.style.display = 'none';
        }
    }
    
// 推荐框颜色渐变    
    function generateColor(index) {
  // 使用 HSL 模式，根据索引生成不同的色调
  const hue = (index * 360) / 35; // 35 是你希望的颜色数量
  return `hsl(${hue}, 100%, 50%)`;
}

const items = document.querySelectorAll('#bestbox ul li');
items.forEach((item, index) => {
  item.style.backgroundColor = generateColor(index + 4); // +1 是因为索引从 0 开始
});

//首页轮播条
			function AutoScroll(obj){
                $(obj).find("ul:first").animate({
                        marginTop:"-22px"
                },500,function(){
                        $(this).css({marginTop:"0px"}).find("li:first").appendTo(this);
                });
			}
			$(document).ready(function(){
				timer=setInterval('AutoScroll(".site-notice")',4000);
			});
			$('#lunbo li').mousemove(function(){clearInterval(timer)});
			$('#lunbo li').mouseout(function(){timer=setInterval('AutoScroll(".site-notice")',5000)});
			

var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?de6784c7f19b11f9d9d70711252011fe";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();

// 打赏JS
function showDonatePopup() {
document.getElementById('donate-popup').style.display = 'flex';
}

function closeDonatePopup() {
document.getElementById('donate-popup').style.display = 'none';
}

// 选项卡切换功能
function initTabSwitching() {
    const tabItems = document.querySelectorAll('.tab-item');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabItems.forEach(function(tabItem) {
        tabItem.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // 移除所有活动状态
            tabItems.forEach(function(item) {
                item.classList.remove('active');
            });
            tabPanes.forEach(function(pane) {
                pane.classList.remove('active');
            });

            // 添加当前选中的活动状态
            this.classList.add('active');
            const targetPane = document.getElementById(targetTab + '-content');
            if (targetPane) {
                targetPane.classList.add('active');
            }

            // 添加点击动画效果
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

// 数据归档月份切换功能
function initArchiveDataSwitching() {
    // 默认加载当前月份（7月）的数据
    if (typeof loadArchiveData === 'function') {
        loadArchiveData('2025', '07');
    }

    // 数据归档自动滚动控制变量
    let archiveAutoSwitchTimer = null;
    let currentArchiveIndex = 0;
    let isArchiveHovering = false;
    const archiveLinks = document.querySelectorAll('.archive-month-link');

    // 绑定月份链接点击事件
    archiveLinks.forEach(function(link, index) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const year = this.getAttribute('data-year');
            const month = this.getAttribute('data-month');

            // 更新活动状态
            archiveLinks.forEach(function(l) {
                l.classList.remove('active');
            });
            this.classList.add('active');

            // 更新当前索引
            currentArchiveIndex = index;

            // 加载对应月份的数据
            if (typeof loadArchiveData === 'function') {
                loadArchiveData(year, month);
            }

            // 重置自动切换
            resetArchiveAutoSwitch();
        });

        // 鼠标悬停时停止自动切换
        link.addEventListener('mouseenter', function() {
            isArchiveHovering = true;
            clearTimeout(archiveAutoSwitchTimer);
        });

        // 鼠标离开时恢复自动切换
        link.addEventListener('mouseleave', function() {
            isArchiveHovering = false;
            startArchiveAutoSwitch();
        });
    });

    // 启动自动切换
    startArchiveAutoSwitch();

    function startArchiveAutoSwitch() {
        if (isArchiveHovering) return;

        archiveAutoSwitchTimer = setTimeout(function() {
            if (!isArchiveHovering && archiveLinks.length > 1) {
                // 添加滚动效果
                const currentLink = archiveLinks[currentArchiveIndex];
                currentLink.classList.add('scrolling');

                // 切换到下一个月份
                currentArchiveIndex = (currentArchiveIndex + 1) % archiveLinks.length;
                const nextLink = archiveLinks[currentArchiveIndex];

                // 延迟一点时间显示滚动效果
                setTimeout(function() {
                    currentLink.classList.remove('scrolling');
                    nextLink.click();
                }, 300);
            }
        }, 5000); // 5秒自动切换
    }

    function resetArchiveAutoSwitch() {
        clearTimeout(archiveAutoSwitchTimer);
        startArchiveAutoSwitch();
    }
}

// 数据归档加载函数
function loadArchiveData(year, month) {
    const loadingEl = document.getElementById('archive-loading');
    const listEl = document.getElementById('archive-list');
    const errorEl = document.getElementById('archive-error');

    // 直接隐藏加载状态和错误状态，不显示加载过渡
    if (loadingEl) loadingEl.style.display = 'none';
    if (errorEl) errorEl.style.display = 'none';
    if (listEl) listEl.style.display = 'block';

    // 构造请求URL
    const url = '?mod=archives&date=' + year + month + '&ajax=1';

    // 发送AJAX请求
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.websites) {
                displayWebsites(data.websites);
            } else {
                showArchiveError('暂无数据');
            }
        })
        .catch(error => {
            console.error('加载失败:', error);
            showArchiveError('加载失败，请稍后重试');
        });
}

function displayWebsites(websites) {
    const listEl = document.getElementById('archive-list');
    if (!listEl) return;

    if (websites.length === 0) {
        showArchiveError('该月份暂无收录网站');
        return;
    }

    // 限制只显示前12条数据
    const limitedWebsites = websites.slice(0, 12);

    let html = '';
    limitedWebsites.forEach(function(site) {
        // 提取域名用于favicon
        let domain = '';
        try {
            const url = new URL(site.web_url.startsWith('http') ? site.web_url : 'http://' + site.web_url);
            domain = url.hostname;
        } catch (e) {
            domain = site.web_url.replace(/^https?:\/\//, '').split('/')[0];
        }

        html += '<li>';
        html += '<span>' + site.web_ctime + '</span>';
        html += '<a href="' + site.web_link + '" title="' + site.web_name + ' - ' + site.web_url + '">';
        html += '<img src="https://t0.gstatic.cn/faviconV2?client=SOCIAL&type=FAVICON&fallback_opts=TYPE,SIZE,URL&size=128&url=http://' + domain + '" width="18" height="18" />';
        html += site.web_name + ' - <small>' + site.web_url + '</small>';
        // 如果是当天发表的网站，添加new图标
        if (site.is_today) {
            html += '<span class="new-icon">new</span>';
        }
        html += '</a>';
        html += '</li>';
    });

    listEl.innerHTML = html;
    listEl.style.display = 'block';
}

function showArchiveError(message) {
    const listEl = document.getElementById('archive-list');
    const errorEl = document.getElementById('archive-error');

    if (listEl) {
        listEl.innerHTML = '';
        listEl.style.display = 'none';
    }
    if (errorEl) {
        errorEl.textContent = message;
        errorEl.style.display = 'block';
    }
}

// 省份标签搜索功能
function initProvinceTagsSearch() {
    const provinceTags = document.querySelectorAll('.province-tag');

    provinceTags.forEach(function(tag) {
        tag.addEventListener('click', function(e) {
            e.preventDefault();

            // 添加点击动画效果
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 150);

            // 获取省份名称
            const province = this.textContent.trim();

            // 构造搜索URL
            const searchUrl = '?mod=search&keyword=' + encodeURIComponent(province);

            // 跳转到搜索页面
            window.location.href = searchUrl;
        });
    });
}

// 添加省份搜索提示
function addProvinceSearchTooltips() {
    const provinceTags = document.querySelectorAll('.province-tag');

    provinceTags.forEach(function(tag) {
        const province = tag.textContent.trim();
        tag.title = '点击搜索' + province + '地区的网站';
    });
}

// 弹窗相关功能
function initDonatePopup() {
    const donatePopup = document.getElementById('donate-popup');
    if (donatePopup) {
        donatePopup.addEventListener('click', function(e) {
            if (e.target === this) {
                closeDonatePopup();
            }
        });
    }

    // ESC键关闭弹窗
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeDonatePopup();
        }
    });
}

// 页面加载完成后初始化功能
$(document).ready(function() {
    // 初始化选项卡切换
    if (typeof initTabSwitching === 'function') {
        initTabSwitching();
    }

    // 初始化数据归档切换
    if (document.querySelector('.archive-month-link')) {
        initArchiveDataSwitching();
    }

    // 初始化省份标签搜索功能
    if (document.querySelector('.province-tag')) {
        initProvinceTagsSearch();
        addProvinceSearchTooltips();
    }

    // 初始化弹窗功能
    initDonatePopup();

    // 初始化统计功能
    if (document.querySelector('.stat-card, .spider-card')) {
        initStatsFeatures();
    }
});

// 统计功能相关
function updateNumberWithAnimation(elementId, newValue) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const oldValue = element.textContent;
    if (oldValue !== String(newValue) && oldValue !== '加载中...') {
        // 添加数字滚动效果
        animateNumber(element, parseInt(oldValue) || 0, newValue, 800);

        // 添加卡片闪烁效果
        const card = element.closest('.stat-card, .spider-card');
        if (card) {
            card.style.boxShadow = '0 0 20px rgba(255, 215, 0, 0.6)';
            setTimeout(() => {
                card.style.boxShadow = '';
            }, 1000);
        }
    } else if (oldValue === '加载中...') {
        element.textContent = newValue;
    }
}

// 数字滚动动画
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    const difference = end - start;

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.round(start + difference * easeOutQuart);

        element.textContent = current;
        element.classList.add('stats-number', 'updated');

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        } else {
            setTimeout(() => {
                element.classList.remove('updated');
            }, 300);
        }
    }

    requestAnimationFrame(updateNumber);
}

// 更新今日访问统计和蜘蛛统计
function updateTodayStats() {
    fetch('?mod=datastats&ajax=1')
        .then(response => response.json())
        .then(data => {
            // 更新今日访问统计
            if (data.today_stats) {
                updateNumberWithAnimation('todayTotalVisits', data.today_stats.total_visits || 0);
                updateNumberWithAnimation('todaySiteVisits', data.today_stats.total_sites || 0);
                updateNumberWithAnimation('todayArticleVisits', data.today_stats.total_articles || 0);
                updateNumberWithAnimation('todayOutlinks', data.today_stats.total_outlinks || 0);
            }

            // 更新蜘蛛统计
            if (data.spider_stats) {
                updateNumberWithAnimation('spiderGoogle', data.spider_stats.google || 0);
                updateNumberWithAnimation('spiderBaidu', data.spider_stats.baidu || 0);
                updateNumberWithAnimation('spiderBing', data.spider_stats.bing || 0);
                updateNumberWithAnimation('spiderSogou', data.spider_stats.sogou || 0);
                updateNumberWithAnimation('spiderSo360', data.spider_stats.so360 || 0);
                updateNumberWithAnimation('spiderBytedance', data.spider_stats.bytedance || 0);
            }
        })
        .catch(() => {
            console.log('今日统计服务不可用');
            // 设置默认值
            const todayElements = ['todayTotalVisits', 'todaySiteVisits', 'todayArticleVisits', 'todayOutlinks'];
            const spiderElements = ['spiderGoogle', 'spiderBaidu', 'spiderBing', 'spiderSogou', 'spiderSo360', 'spiderBytedance'];

            todayElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) element.textContent = '0';
            });

            spiderElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) element.textContent = '0';
            });
        });
}

// 添加卡片点击效果
function addCardClickEffects() {
    document.querySelectorAll('.stat-card, .spider-card').forEach(card => {
        card.addEventListener('click', function() {
            // 点击波纹效果
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = (rect.width / 2 - size / 2) + 'px';
            ripple.style.top = (rect.height / 2 - size / 2) + 'px';

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// 初始化统计功能
function initStatsFeatures() {
    // 添加波纹动画CSS
    const rippleStyle = document.createElement('style');
    rippleStyle.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(rippleStyle);

    // 立即更新统计数据
    updateTodayStats();
    addCardClickEffects();

    // 设置定时器 - 今日统计每5分钟更新
    setInterval(updateTodayStats, 300000);
}



