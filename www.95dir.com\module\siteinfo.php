<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '站点详细';
$pageurl = '?mod=siteinfo';
$tempfile = 'siteinfo.html';
$table = $DB->table('webdata');

$web_id = intval($_GET['wid']);
$cache_id = $web_id;

if (!$smarty->isCached($tempfile, $cache_id)) {
	$where = "w.web_status=3 AND w.web_id=$web_id";
	$web = get_one_website($where);
	if (!$web) {
		unset($web);
		redirect('./?mod=index');
	}

	$DB->query("UPDATE $table SET web_views=web_views+1 WHERE web_id=".$web['web_id']." LIMIT 1");

	// 同时记录今日出站统计（VIP网站直链访问统计）
	$today = date('Y-m-d');
	$spider_table = $DB->table('spider_stats');

	// 检查今天的记录是否存在 - 添加错误处理
	try {
		$today_record = $DB->fetch_one("SELECT id FROM $spider_table WHERE stat_date = '$today'");

		if ($today_record) {
			// 更新今日出站次数
			$DB->query("UPDATE $spider_table SET total_outlinks = total_outlinks + 1, updated_at = NOW() WHERE stat_date = '$today'");
		} else {
			// 创建今日记录
			$DB->query("INSERT INTO $spider_table (stat_date, total_outlinks, created_at, updated_at) VALUES ('$today', 1, NOW(), NOW())");
		}
	} catch (Exception $e) {
		// 如果spider_stats表不存在或有其他错误，忽略错误继续执行
		// 可以记录日志但不影响页面显示
	}

	$cate = get_one_category($web['cate_id']);
	$user = get_one_user($web['user_id']);

	// 优化SEO标题 - 更具描述性
	$seo_title = $web['web_name'] . ' - ' . (isset($cate['cate_name']) ? $cate['cate_name'] : '未分类') . '网站详情 - ' . $options['site_name'];
	$smarty->assign('site_title', $seo_title);

	// 优化关键词 - 结合网站名称、分类和标签
	$seo_keywords = array();
	if (!empty($web['web_tags'])) {
		$seo_keywords[] = $web['web_tags'];
	}
	$seo_keywords[] = $web['web_name'];
	if (isset($cate['cate_name'])) {
		$seo_keywords[] = $cate['cate_name'];
	}
	$seo_keywords[] = $web['web_url'];
	$seo_keywords[] = '网站收录';
	$seo_keywords[] = '网站目录';
	if (!empty($options['site_keywords'])) {
		$seo_keywords[] = $options['site_keywords'];
	}
	$smarty->assign('site_keywords', implode(',', array_unique($seo_keywords)));

	// 优化描述 - 更丰富的描述信息
	$seo_description = '';
	if (!empty($web['web_intro'])) {
		$seo_description = $web['web_intro'];
	} else {
		$seo_description = $web['web_name'] . '是一个优质的' . (isset($cate['cate_name']) ? $cate['cate_name'] : '网站');
	}

	// 添加统计信息到描述中
	$stats_info = array();
	if (isset($web['web_views']) && $web['web_views'] > 0) {
		$stats_info[] = '访问量' . $web['web_views'];
	}
	if (isset($web['web_grank']) && $web['web_grank'] > 0) {
		$stats_info[] = '百度收录' . $web['web_grank'];
	}

	if (!empty($stats_info)) {
		$seo_description .= '，' . implode('，', $stats_info);
	}

	$seo_description .= '。收录于' . $options['site_name'] . (isset($cate['cate_name']) ? $cate['cate_name'] : '') . '分类目录。';

	// 确保描述长度适中（150-160字符最佳）
	if (mb_strlen($seo_description, 'UTF-8') > 160) {
		$seo_description = mb_substr($seo_description, 0, 157, 'UTF-8') . '...';
	}

	$smarty->assign('site_description', $seo_description);
	$smarty->assign('site_path', get_sitepath($cate['cate_mod'], $web['cate_id']).' &raquo; '.$pagename);
	$smarty->assign('site_rss', get_rssfeed($cate['cate_mod'], $web['cate_id']));

	$smarty->assign('cate_id', isset($cate['cate_id']) ? $cate['cate_id'] : 0);
	$smarty->assign('cate_name', isset($cate['cate_name']) ? $cate['cate_name'] : '未分类');
	$smarty->assign('cate_keywords', isset($cate['cate_keywords']) ? $cate['cate_keywords'] : '');
	$smarty->assign('cate_description', isset($cate['cate_description']) ? $cate['cate_description'] : '');

	$web['web_furl'] = format_url($web['web_url']);
	$web['web_pic'] = get_webthumb($web['web_pic']);
	$web['web_ip'] = isset($web['web_ip']) ? long2ip($web['web_ip']) : '';
	$web['web_ctime'] = date('Y-m-d', $web['web_ctime']);
	$web['web_utime'] = isset($web['web_utime']) ? date('Y-m-d', $web['web_utime']) : date('Y-m-d');

	// PageRank处理
	$web['web_prank'] = isset($web['web_grank']) ? $web['web_grank'] : 0;

	/** tags */
	$web_tags = get_format_tags($web['web_tags']);
	$smarty->assign('web_tags', $web_tags);

    $smarty->assign('web', $web);
	$smarty->assign('user', $user);
	$smarty->assign('prev_website', get_prev_website($web['web_id']));
	$smarty->assign('next_website', get_next_website($web['web_id']));
	$smarty->assign('related_website', get_websites($web['cate_id'], 10, false, false, 'ctime'));
}

smarty_output($tempfile, $cache_id);
?>