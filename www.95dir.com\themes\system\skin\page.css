@charset "utf-8";
/* custom */
*html {background-image: url(about:blank); background-attachment: fixed;}
body {background: #fff;}
body, td, th, input, select, textarea {color: #333; font-family: 微软雅黑;}
a {color: #333; text-decoration: none;}
a:hover {color: #f30; text-decoration: underline;}
.sel {background: #fff url(ipt.png); border: solid 1px #dadada; height: 23px;}
.ipt {background: #fff url(ipt.png); border-top: solid 1px #dadada; border-left: solid 1px #dadada; border-right: solid 1px #f3f3f3; border-bottom: solid 1px #f3f3f3; padding: 4px;}
.btn {background: #71ad29; border: solid 1px #5e9a22; color: #fff; height: 25px; padding: 0 5px; _padding: 0 3px;}
.tips {color: #999; padding-left: 5px;}
/* wrapper */
#wrapper {margin: 0 auto; width: 100%;}
/* title */
.title {background: #fff; border-bottom: solid 1px #fadddd; display: block; height: 35px;}
.title em {background: #fee; border: solid 1px #fadddd; border-bottom: 0; color: #be0a0a; float: left; height: 28px; line-height: 27px; margin: 7px 0 0 10px; padding: 0px 10px;}
.title span {display: block; float: left; height: 28px; font-weight: normal; line-height: 27px; margin: 7px 0 0 10px; padding: 0px 10px;}
/* toolbar */
.toolbar {background: #fee; border-bottom: solid 1px #fadddd; padding: 5px;}
/*search*/
.search {padding-top: 3px; position: absolute; right: 5px; z-index: 100;}
/* listbox */
.listbox {}
.listbox table {}
.listbox table tr th {background: url(title.png) repeat-x; height: 30px;}
.listbox table tr td {border-bottom: solid 1px #ececec; height: 30px; text-align: center;}
.listbox table tr .ltext {text-align: left;}
.listbox table tr .data {font-size: 10px;}
.gre {color: #080;}
.red {color: #f00;}
.org {color: #f30;}
.over {background: #ffc;}
/* pagebox */
.pagebox {display: block; padding: 8px;}
.total_page, .jump_page {background: #c01616; border: solid 1px #c92308; color: #fff; display: block; float: left; margin-right: 5px; padding: 2px 6px;}
.first_page, .last_page, .prev_page, .next_page, .pages {background: #fffafa; border: 1px solid #c92308; color: #ac0505; display: block; float: left; margin-right: 5px; padding: 2px 6px; text-decoration: none;}
.current {background: #c01616; border: solid 1px #c92308; color: #fff; display: block; float: left; margin-right: 5px; padding: 2px 6px;}
/* formbox */
.formbox {padding-top: 10px;}
.formbox table {}
.formbox table tr th {font-weight: normal; padding: 12px 0; text-align: right; width: 20%;}
.formbox table tr td {line-height: 25px;}
.formbox .btnbox {height: 40px;}