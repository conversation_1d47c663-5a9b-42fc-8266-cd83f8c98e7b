<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
{#include file="script.html"#}
</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
    <div id="mainbox" class="clearfix">
    	    <div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>VIP特权排行榜 TOP10</h3>
                <ul class="toplist">
                   	{#foreach from=get_websites(0, 10, 'views') item=quick name=hot_website#}
                   	<li><span>{#$smarty.foreach.hot_website.iteration#}.</span><a href="{#$quick.web_link#}" title="{#$quick.web_name#}">{#$quick.web_name#}</a> - <em><a href="{#$quick.web_furl#}" target="_blank" onClick="clickout({#$quick.web_id#})">{#$quick.web_url#}</a></em><em1>{#$quick.web_outstat#}</em1></li>
                   	{#/foreach#}
               	</ul>
            </div>
            <div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>推荐排行榜 TOP10</h3>
                <ul class="toplist">
                   	{#foreach from=get_websites(0, 10, false, 'views') item=quick name=hot_website#}
                   	<li><span>{#$smarty.foreach.hot_website.iteration#}.</span><a href="{#$quick.web_link#}" title="{#$quick.web_name#}">{#$quick.web_name#}</a> - <em><a href="{#$quick.web_link#}" target="_blank" onClick="clickout({#$quick.web_id#})">{#$quick.web_url#}</a></em><em1>{#$quick.web_outstat#}</em1></li>
                   	{#/foreach#}
               	</ul>
            </div>
    	    <div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>电脑网络排行榜 TOP10</h3>
                <ul class="toplist">
                   	{#foreach from=get_websites(3, 10, false, false, 'views') item=hot name=hot_website#}
                   	<li><span>{#$smarty.foreach.hot_website.iteration#}.</span><a href="{#$hot.web_link#}" title="{#$hot.web_name#}">{#$hot.web_name#}</a> - <em><a href="{#$hot.web_link#}" target="_blank" onClick="clickout({#$hot.web_id#})">{#$hot.web_url#}</a></em><em1>{#$hot.web_outstat#}</em1></li>
                   	{#/foreach#}
               	</ul>
            </div>
            <div class="topsite">
            <h3><i class="fas fa-bar-chart"></i>生活服务排行榜 TOP10</h3>
                <ul class="toplist">
                   	{#foreach from=get_websites(4, 10, false, false, 'views') item=hot name=hot_website#}
                   	<li><span>{#$smarty.foreach.hot_website.iteration#}.</span><a href="{#$hot.web_link#}" title="{#$hot.web_name#}">{#$hot.web_name#}</a> - <em><a href="{#$hot.web_link#}" target="_blank" onClick="clickout({#$hot.web_id#})">{#$hot.web_url#}</a></em><em1>{#$hot.web_outstat#}</em1></li>
                   	{#/foreach#}
               	</ul>
            </div>
            <div class="topsite">
            <h3><i class="fas fa-bar-chart"></i>休闲娱乐排行榜 TOP10</h3>
                <ul class="toplist">
                   	{#foreach from=get_websites(5, 10, false, false, 'views') item=hot name=hot_website#}
                   	<li><span>{#$smarty.foreach.hot_website.iteration#}.</span><a href="{#$hot.web_link#}" title="{#$hot.web_name#}">{#$hot.web_name#}</a> - <em><a href="{#$hot.web_link#}" target="_blank" onClick="clickout({#$hot.web_id#})">{#$hot.web_url#}</a></em><em1>{#$hot.web_outstat#}</em1></li>
                   	{#/foreach#}
               	</ul>
            </div>
    	    <div class="topsite">
            <h3><i class="fas fa-bar-chart"></i>教育文化排行榜 TOP10</h3>
                <ul class="toplist">
                   	{#foreach from=get_websites(6, 10, false, false, 'views') item=hot name=hot_website#}
                   	<li><span>{#$smarty.foreach.hot_website.iteration#}.</span><a href="{#$hot.web_link#}" title="{#$hot.web_name#}">{#$hot.web_name#}</a> - <em><a href="{#$hot.web_link#}" target="_blank" onClick="clickout({#$hot.web_id#})">{#$hot.web_url#}</a></em><em1>{#$hot.web_outstat#}</em1></li>
                   	{#/foreach#}
               	</ul>
            </div>
            <div class="topsite">
            <h3><i class="fas fa-bar-chart"></i>行业企业排行榜 TOP10</h3>
                <ul class="toplist">
                   	{#foreach from=get_websites(7, 10, false, false, 'views') item=hot name=hot_website#}
                   	<li><span>{#$smarty.foreach.hot_website.iteration#}.</span><a href="{#$hot.web_link#}" title="{#$hot.web_name#}">{#$hot.web_name#}</a> - <em><a href="{#$hot.web_link#}" target="_blank" onClick="clickout({#$hot.web_id#})">{#$hot.web_url#}</a></em><em1>{#$hot.web_outstat#}</em1></li>
                   	{#/foreach#}
               	</ul>
            </div>
            <div class="topsite">
            <h3><i class="fas fa-bar-chart"></i>综合其他排行榜 TOP10</h3>
                <ul class="toplist">
                   	{#foreach from=get_websites(8, 10, false, false, 'views') item=hot name=hot_website#}
                   	<li><span>{#$smarty.foreach.hot_website.iteration#}.</span><a href="{#$hot.web_link#}" title="{#$hot.web_name#}">{#$hot.web_name#}</a> - <em><a href="{#$hot.web_link#}" target="_blank" onClick="clickout({#$hot.web_id#})">{#$hot.web_url#}</a></em><em1>{#$hot.web_outstat#}</em1></li>
                   	{#/foreach#}
               	</ul>
            </div>
    	    <div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>人工智能排行榜 TOP10</h3>
                <ul class="toplist">
                   	{#foreach from=get_websites(1, 10, false, false, 'views') item=hot name=hot_website#}
                   	<li><span>{#$smarty.foreach.hot_website.iteration#}.</span><a href="{#$hot.web_link#}" title="{#$hot.web_name#}">{#$hot.web_name#}</a> - <em><a href="{#$hot.web_link#}" target="_blank" onClick="clickout({#$hot.web_id#})">{#$hot.web_url#}</a></em><em1>{#$hot.web_outstat#}</em1></li>
                   	{#/foreach#}
               	</ul>
            </div>
        	<div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>入站排行榜 TOP10</h3>
                <ul class="toplist">
                   	{#foreach from=get_websites(0, 10, false, false, 'instat') item=instat name=instat_website#}
                   	<li><span>{#$smarty.foreach.instat_website.iteration#}.</span><a href="{#$instat.web_link#}" title="{#$instat.web_name#}">{#$instat.web_name#}</a> - <em><a href="{#$instat.web_link#}" target="_blank" onClick="clickout({#$instat.web_id#})">{#$instat.web_url#}</a></em><em1>{#$instat.web_instat#}</em1></li>
                   	{#/foreach#}
               	</ul>
            </div>
        	<div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>出站排行榜 TOP10</h3>
                <ul class="toplist">
                   	{#foreach from=get_websites(0, 10, false, false, 'outstat') item=outstat name=outstat_website#}
                   	<li><span>{#$smarty.foreach.outstat_website.iteration#}.</span><a href="{#$outstat.web_link#}" title="{#$outstat.web_name#}">{#$outstat.web_name#}</a> - <em><a href="{#$outstat.web_link#}" target="_blank" onClick="clickout({#$outstat.web_id#})">{#$outstat.web_url#}</a></em><em1>{#$outstat.web_outstat#}</em1></li>
                   	{#/foreach#}
               	</ul>
            </div>
        	<div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>最新收录 TOP10</h3>
                <ul class="toplist">
                   	{#foreach from=get_websites(0, 10, false, false, 'ctime') item=new name=new_website#}
                   	<li><span>{#$smarty.foreach.new_website.iteration#}.</span><a href="{#$new.web_link#}" title="{#$new.web_name#}">{#$new.web_name#}</a> - <em><a href="{#$new.web_link#}" target="_blank" onClick="clickout({#$new.web_id#})">{#$new.web_url#}</a></em><em1>{#$new.web_views#}</em1></li>
                   	{#/foreach#}
               	</ul>
            </div>
        	<div class="topsite">
            	<h3><i class="fas fa-bar-chart"></i>热门浏览 TOP10</h3>
                <ul class="toplist">
                   	{#foreach from=get_websites(0, 10, false, false, 'views') item=hot name=hot_website#}
                   	<li><span>{#$smarty.foreach.hot_website.iteration#}.</span><a href="{#$hot.web_link#}" title="{#$hot.web_name#}">{#$hot.web_name#}</a> - <em><a href="{#$hot.web_link#}" target="_blank" onClick="clickout({#$hot.web_id#})">{#$hot.web_url#}</a></em><em1>{#$hot.web_views#}</em1></li>
                   	{#/foreach#}
               	</ul>
            </div>
        <!--<div id="mainbox-right">
            <div style="height: 250px;">
            {#get_adcode(7)#}
            </div>
            <div style="margin-top: 15px;">{#get_adcode(7)#}</div>
        </div>-->
    </div>
    {#include file="footer.html"#}
</div>
</body>
</html>