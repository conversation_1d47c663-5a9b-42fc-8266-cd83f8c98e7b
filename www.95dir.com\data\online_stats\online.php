<?php
// online.php 修复版（带历史访问统计）
$timeout = 300; // 5分钟超时
$logFile = __DIR__ . '/.online_log';

// 设置正确的Content-Type
header('Content-Type: application/json');

try {
    // 检测必要参数是否存在
    $isWebRequest = isset($_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']);

    // 读取现有数据
    if (file_exists($logFile)) {
        $content = file_get_contents($logFile);

        // 清理可能的BOM和编码问题
        $content = trim($content);
        $content = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $content); // 移除非ASCII字符
        $content = str_replace([' ', "\n", "\r"], '', $content); // 移除空格和换行

        $data = json_decode($content, true);

        // 修复损坏的数据结构
        if (!is_array($data) || !isset($data['active']) || !isset($data['total'])) {
            // 如果文件损坏，恢复历史数据
            $data = ['active' => [], 'total' => 136579]; // 恢复您的历史访问数据
        }

        // 清理非法的active数据
        if (!is_array($data['active'])) {
            $data['active'] = [];
        }

        // 确保total是数字，如果为0则恢复历史数据
        if (!is_numeric($data['total']) || $data['total'] < 136579) {
            $data['total'] = 136579; // 恢复历史数据
        }
    } else {
        // 如果文件不存在，创建并恢复历史数据
        $data = ['active' => [], 'total' => 136579];
    }

    // 清理过期记录
    $currentTime = time();
    $cleanedActive = [];

    foreach($data['active'] as $key => $timestamp) {
        if (is_numeric($timestamp) && ($currentTime - $timestamp) <= $timeout) {
            $cleanedActive[$key] = $timestamp;
        }
    }

    $data['active'] = $cleanedActive;

    // 仅在Web请求时更新记录
    if($isWebRequest) {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        $remoteAddr = $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';

        // 过滤掉爬虫和机器人
        $botPatterns = [
            'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget',
            'python', 'java', 'go-http', 'okhttp', 'apache-httpclient'
        ];

        $isBot = false;
        foreach ($botPatterns as $pattern) {
            if (stripos($userAgent, $pattern) !== false) {
                $isBot = true;
                break;
            }
        }

        if (!$isBot) {
            $Hash = hash('sha256', $remoteAddr . $userAgent);

            // 如果是新会话则增加总计数
            if (!isset($data['active'][$Hash])) {
                $data['total']++;
            }

            $data['active'][$Hash] = $currentTime;
        }
    }

    // 保存数据（原子操作，确保UTF-8编码）
    $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    $tempFile = $logFile . '.tmp';

    // 确保写入UTF-8格式
    if (file_put_contents($tempFile, $jsonData, LOCK_EX) !== false) {
        if (rename($tempFile, $logFile)) {
            // 验证文件是否正确写入
            $verify = file_get_contents($logFile);
            if (json_decode($verify, true) === null) {
                // 如果验证失败，删除损坏的文件
                unlink($logFile);
            }
        }
    }

    // 返回在线人数和历史总数
    echo json_encode([
        'online' => count($data['active']),
        'total' => (int)$data['total'],
        'status' => 'success'
    ]);

} catch (Exception $e) {
    error_log("Online Counter Error: " . $e->getMessage());
    echo json_encode([
        'online' => 0,
        'total' => 0,
        'status' => 'error',
        'message' => 'Service temporarily unavailable'
    ]);
}
?>
